import { Context } from "koishi";
import { ConversationFlowAnalyzer } from "../../services/ConversationFlowAnalyzer";
import {
    AtMentionStrategy,
    ChannelState,
    ConversationFlowStrategy,
    ReplyDecision,
    ReplyStrategy,
    ThresholdStrategy,
    WillingnessService,
} from "../../services/WillingnessService";
import { DataManager } from "../../services/worldstate/DataManager";
import { ConversationState, MessageContext, Middleware } from "../base";
import { ReplyConditionConfig } from "../../types/middleware";


export class CheckReplyCondition extends Middleware {
    private strategies: ReplyStrategy[] = [];
    private channelStates = new Map<string, ChannelState>();
    private delayTimers = new Map<string, NodeJS.Timeout>();
    private willingnessService: WillingnessService;
    private decayTimer?: NodeJS.Timeout;
    private dataManager: DataManager;

    constructor(public ctx: Context, public config: ReplyConditionConfig) {
        super("check-reply-condition", ctx, null, config);

        this.dataManager = ctx["yesimbot.data"];

        this.willingnessService = new WillingnessService(config.Advanced?.Willingness);
        this.initializeStrategies();
        this.startDecayTimer();
    }

    private initializeStrategies(): void {
        // 初始化@提及策略
        if (this.config.Strategies.AtMention.Enabled) {
            this.strategies.push(new AtMentionStrategy(this.config.Strategies.AtMention));
        }

        // 初始化阈值策略
        if (this.config.Strategies.Threshold.Enabled) {
            this.strategies.push(new ThresholdStrategy(this.config.Strategies.Threshold));
        }

        // 初始化对话流策略
        if (this.config.Strategies.ConversationFlow.Enabled) {
            const flowAnalyzer = new ConversationFlowAnalyzer(this.ctx);
            this.strategies.push(new ConversationFlowStrategy(this.config.Strategies.ConversationFlow, flowAnalyzer));
        }
    }

    private startDecayTimer(): void {
        if (!this.config.Advanced?.Willingness) return;

        // 每分钟衰减一次
        this.decayTimer = setInterval(() => {
            this.willingnessService.decay();
        }, 60000);
    }

    async execute(ctx: MessageContext, next: () => Promise<void>): Promise<void> {
        const channelId = ctx.koishiSession.channelId;
        const userId = ctx.koishiSession.author.id;
        const now = Date.now();

        // 基础检查
        if (ctx.koishiSession.author.isBot) return;
        if (!this.isAllowedChannel(channelId)) return;
        if (this.isChannelProcessing(channelId)) return;

        // 获取或创建频道状态
        const state = this.getOrCreateChannelState(channelId);

        // 更新意愿值
        this.willingnessService.updateWillingness(channelId, ctx.isMentioned, ctx.koishiSession.content);
        state.willingness = this.willingnessService.getWillingness(channelId);

        // 检查是否需要取消现有定时器
        if (this.shouldCancelExistingTimer(state, userId, now)) {
            this.cancelExistingTimer(channelId);
        } else if (this.delayTimers.has(channelId)) {
            // 如果不取消且已有定时器，直接返回
            return;
        }

        // 更新状态
        state.lastMessageTime = now;
        state.lastMessageUser = userId;

        // 启动新的延迟处理
        this.startDelayedProcessing(ctx, next, state);
    }

    private isAllowedChannel(channelId: string): boolean {
        return this.config.Channels.some((slot) => slot.includes(channelId));
    }

    private isChannelProcessing(channelId: string): boolean {
        const state = this.channelStates.get(channelId);
        return state?.processing || false;
    }

    private getOrCreateChannelState(channelId: string): ChannelState {
        let state = this.channelStates.get(channelId);
        if (!state) {
            state = {
                willingness: 0,
                processing: false,
                lastMessageTime: 0,
                lastMessageUser: "",
            };
            this.channelStates.set(channelId, state);
        }
        return state;
    }

    private shouldCancelExistingTimer(state: ChannelState, userId: string, now: number): boolean {
        // 如果是同一用户在短时间内的连续消息，取消之前的定时器
        return state.lastMessageUser === userId && now - state.lastMessageTime < this.config.Timing.SameUserThreshold;
    }

    private cancelExistingTimer(channelId: string): void {
        const timer = this.delayTimers.get(channelId);
        if (timer) {
            clearTimeout(timer);
            this.delayTimers.delete(channelId);
        }
    }

    private startDelayedProcessing(ctx: MessageContext, next: () => Promise<void>, state: ChannelState): void {
        const channelId = ctx.koishiSession.channelId;
        const waitTime = this.config.Timing.WaitTime;

        const timer = setTimeout(async () => {
            try {
                await this.processMessage(ctx, next, state);
            } catch (error) {
                state.processing = false;
                this.delayTimers.delete(channelId);

                // 将错误直接抛出，会传递到最终的错误处理
                throw error;
            } finally {
                this.delayTimers.delete(channelId);
            }
        }, waitTime);

        this.delayTimers.set(channelId, timer);
    }

    private async processMessage(ctx: MessageContext, next: () => Promise<void>, state: ChannelState): Promise<void> {
        const channelId = ctx.koishiSession.channelId;

        // 标记为处理中
        state.processing = true;

        try {
            // 评估所有策略
            const decisions = await Promise.all(
                this.strategies.filter((strategy) => strategy.enabled).map((strategy) => strategy.evaluate(ctx, state))
            );

            // 找到应该回复的决策
            const positiveDecision = decisions.find((decision) => decision.shouldReply);
            const shouldReply = !!positiveDecision || this.config.TestMode;

            // 记录日志
            this.logDecision(channelId, ctx.koishiSession.author.id, decisions, shouldReply);

            if (shouldReply) {
                // 重置意愿值
                this.willingnessService.resetAfterReply(channelId);
                state.willingness = this.willingnessService.getWillingness(channelId);

                // 开始一个新的对话回合 (Turn)
                //const newTurn = await this.dataManager.startNewTurn(ctx.koishiSession.platform, channelId);
                //ctx.currentTurnId = newTurn.id;
                //this.ctx.logger.info(`[Turn] Started new turn: ${ctx.currentTurnId}`);

                // 设置处理状态并继续中间件链
                ctx.state = ConversationState.PROCESSING;
                await next();
            }
        } finally {
            state.processing = false;
        }
    }

    private logDecision(channelId: string, userId: string, decisions: ReplyDecision[], shouldReply: boolean): void {
        const strategyResults = decisions
            .map((d) => `${d.strategy}:${d.shouldReply ? "✓" : "✗"}${Math.round(d.confidence * 100)}%`)
            .join(", ");

        this.ctx.logger.info(
            `[CheckReplyCondition] 回复决策: ${channelId} | 用户:${userId} | 策略: ${strategyResults} | 结果: ${
                shouldReply ? "回复" : "不回复"
            }`
        );
    }

    // 清理资源
    public destroy(): void {
        // 清理衰减定时器
        if (this.decayTimer) {
            clearInterval(this.decayTimer);
            this.decayTimer = undefined;
        }

        // 清理延迟处理定时器
        for (const timer of this.delayTimers.values()) {
            clearTimeout(timer);
        }
        this.delayTimers.clear();
    }

    // 提供状态查询接口（用于调试）
    public getChannelState(channelId: string): ChannelState | undefined {
        return this.channelStates.get(channelId);
    }

    public getWillingness(channelId: string): number {
        return this.willingnessService.getWillingness(channelId);
    }
}
