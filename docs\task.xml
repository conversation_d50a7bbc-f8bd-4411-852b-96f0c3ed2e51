<file_content path="external/YesImBot/packages/core/src/middleware">
├── base.ts
├── CheckReplyCondition.ts
├── DatabaseStorage.ts
├── ErrorHandling.ts
├── LLMProcessing.ts
└── ResponseHandling.ts

<file_content path="external/YesImBot/packages/core/src/middleware/base.ts">
  1 | import { Context, Session } from "koishi";
  2 | import type { GenerateTextResult } from "xsai";
  3 | import { DefaultPlatform, OneBotPlatform, PlatformAdapter } from "../services/PlatformAdapter";
  4 | import { Scenario } from "../services/scenario/Scenario";
  5 |
  6 | /**
  7 |  * 会话状态枚举
  8 |  * 简化为三个核心状态
  9 |  */
 10 | export enum ConversationState {
 11 |     IDLE, // 空闲状态，等待新消息触发
 12 |     PROCESSING, // 处理中状态
 13 |     RESPONDING, // 响应中状态
 14 | }
 15 |
 16 | /**
 17 |  * 消息上下文
 18 |  * 在中间件链中传递的上下文对象
 19 |  */
 20 | export class MessageContext {
 21 |     // 当前会话状态
 22 |     public state: ConversationState = ConversationState.IDLE;
 23 |
 24 |     // LLM响应和处理后的响应
 25 |     public llmResponse?: GenerateTextResult;
 26 |     public processedResponse?: string[];
 27 |
 28 |     public isMentioned: boolean = false;
 29 |
 30 |     // heartbeat触发次数计数器
 31 |     public heartbeatCount: number = 0;
 32 |
 33 |     public currentScenario: Scenario;
 34 |
 35 |     public platform: PlatformAdapter;
 36 |
 37 |     constructor(
 38 |         // Koishi上下文对象
 39 |         public koishiContext: Context,
 40 |         // Koishi会话对象
 41 |         public koishiSession: Session,
 42 |
 43 |         public allowedChannels: string[]
 44 |     ) {
 45 |         this.isMentioned = koishiSession.stripped.atSelf;
 46 |         let platformAdapter: PlatformAdapter;
 47 |         if (koishiSession.platform === "onebot") {
 48 |             platformAdapter = new OneBotPlatform(koishiSession);
 49 |         } else {
 50 |             platformAdapter = new DefaultPlatform(koishiSession);
 51 |         }
 52 |         this.platform = platformAdapter;
 53 |     }
 54 |
 55 |     /**
 56 |      * 转换会话状态
 57 |      */
 58 |     async transitionTo(newState: ConversationState): Promise<void> {
 59 |         this.state = newState;
 60 |     }
 61 | }
 62 |
 63 | /**
 64 |  * 中间件接口
 65 |  */
 66 | export abstract class Middleware {
 67 |     public readonly name: string; // 中间件名称
 68 |     protected readonly ctx: Context; // Koishi上下文对象
 69 |     protected readonly services: any; // 服务对象
 70 |     protected readonly config: any; // 配置对象
 71 |
 72 |     constructor(name: string, ctx: Context, services?: any, config?: any) {
 73 |         this.name = name;
 74 |         this.ctx = ctx;
 75 |         this.services = services;
 76 |         this.config = config;
 77 |     }
 78 |
 79 |     // 执行中间件
 80 |     abstract execute(ctx: MessageContext, next: () => Promise<void>): Promise<void>;
 81 | }
 82 |
 83 | /**
 84 |  * 中间件管理器
 85 |  * 负责注册和执行中间件链
 86 |  */
 87 | export class MiddlewareManager {
 88 |     // 中间件列表
 89 |     public middlewares: Middleware[] = [];
 90 |
 91 |     /**
 92 |      * 注册中间件
 93 |      */
 94 |     use(middleware: Middleware): this {
 95 |         this.middlewares.push(middleware);
 96 |         return this;
 97 |     }
 98 |
 99 |     /**
100 |      * 执行中间件链
101 |      */
102 |     async execute(ctx: MessageContext): Promise<void> {
103 |         await this.executeFrom(ctx, 0);
104 |     }
105 |
106 |     /**
107 |      * 从指定位置开始执行中间件链
108 |      */
109 |     async executeFrom(ctx: MessageContext, startIndex: number): Promise<void> {
110 |         const dispatch = async (index: number): Promise<void> => {
111 |             if (index >= this.middlewares.length) return;
112 |             const middleware = this.middlewares[index];
113 |             await middleware.execute(ctx, () => dispatch(index + 1));
114 |         };
115 |         await dispatch(startIndex);
116 |     }
117 |
118 |     /**
119 |      * 获取指定名称的中间件
120 |      */
121 |     public getMiddleware<T extends Middleware>(name: string): T | undefined {
122 |         return this.middlewares.find((m) => m.name === name) as T;
123 |     }
124 |
125 |     public findIndex(name: string): number {
126 |         return this.middlewares.findIndex((m) => m.name === name);
127 |     }
128 | }

</file_content>

<file_content path="external/YesImBot/packages/core/src/middleware/CheckReplyCondition.ts">
  1 | import { Computed, Context, Random } from "koishi";
  2 | import { ConversationFlowAnalyzer } from "../services/ConversationFlowAnalyzer";
  3 | import { ChatMessage } from "../types/model";
  4 | import { getChannelType } from "../utils";
  5 | import { ConversationState, MessageContext, Middleware } from "./base";
  6 |
  7 | // 简化的配置接口
  8 | export interface ReplyConditionConfig {
  9 |     // 基础配置
 10 |     Channels: string[][];
 11 |     TestMode?: boolean;
 12 |
 13 |     // 回复策略配置
 14 |     Strategies: {
 15 |         AtMention: {
 16 |             Enabled: boolean;
 17 |             // 暂时不清楚Computed的用法，希望可以针对不同群组，不同用户设定不同的回复概率
 18 |             // Probability: number | Computed<number>;
 19 |             Probability: number;
 20 |         };
 21 |         Threshold: {
 22 |             Enabled: boolean;
 23 |             Value: number;
 24 |         };
 25 |         ConversationFlow: {
 26 |             Enabled: boolean;
 27 |             ConfidenceThreshold: number;
 28 |         };
 29 |     };
 30 |
 31 |     // 时间控制
 32 |     Timing: {
 33 |         WaitTime: number;
 34 |         SameUserThreshold: number;
 35 |     };
 36 |
 37 |     // 高级功能（可选）
 38 |     Advanced?: {
 39 |         Willingness?: {
 40 |             MessageIncrease: number;
 41 |             AtIncrease: number;
 42 |             DecayRate: number;
 43 |             RetentionAfterReply: number;
 44 |             Keywords?: {
 45 |             	List: string[];
 46 |             	Increase: number;
 47 |             }
 48 |         };
 49 |     };
 50 | }
 51 |
 52 | // 回复决策结果
 53 | interface ReplyDecision {
 54 |     shouldReply: boolean;
 55 |     strategy: string;
 56 |     confidence: number;
 57 |     waitTime: number;
 58 |     reason: string;
 59 | }
 60 |
 61 | // 频道状态
 62 | interface ChannelState {
 63 |     willingness: number;
 64 |     processing: boolean;
 65 |     lastMessageTime: number;
 66 |     lastMessageUser: string;
 67 | }
 68 |
 69 | // 回复策略接口
 70 | interface ReplyStrategy {
 71 |     name: string;
 72 |     enabled: boolean;
 73 |     evaluate(ctx: MessageContext, state: ChannelState): Promise<ReplyDecision>;
 74 | }
 75 |
 76 | // @提及策略
 77 | class AtMentionStrategy implements ReplyStrategy {
 78 |     name = "at_mention";
 79 |     enabled: boolean;
 80 |
 81 |     constructor(private config: ReplyConditionConfig["Strategies"]["AtMention"]) {
 82 |         this.enabled = config.Enabled;
 83 |     }
 84 |
 85 |     async evaluate(ctx: MessageContext): Promise<ReplyDecision> {
 86 |         if (!ctx.isMentioned) {
 87 |             return {
 88 |                 shouldReply: false,
 89 |                 strategy: this.name,
 90 |                 confidence: 0,
 91 |                 waitTime: 1000,
 92 |                 reason: "not_mentioned",
 93 |             };
 94 |         }
 95 |
 96 |         const shouldReply = Random.bool(this.config.Probability);
 97 |
 98 |         return {
 99 |             shouldReply,
100 |             strategy: this.name,
101 |             confidence: shouldReply ? 1.0 : 0,
102 |             waitTime: 1000, // 快速响应@消息
103 |             reason: shouldReply ? "direct_mention" : "mention_probability_failed",
104 |         };
105 |     }
106 | }
107 |
108 | // 阈值策略
109 | class ThresholdStrategy implements ReplyStrategy {
110 |     name = "threshold";
111 |     enabled: boolean;
112 |
113 |     constructor(private config: ReplyConditionConfig["Strategies"]["Threshold"]) {
114 |         this.enabled = config.Enabled;
115 |     }
116 |
117 |     async evaluate(ctx: MessageContext, state: ChannelState): Promise<ReplyDecision> {
118 |         const threshold = this.config.Value;
119 |         const confidence = Math.min(state.willingness / threshold, 1.0);
120 |         const shouldReply = state.willingness >= threshold;
121 |
122 |         return {
123 |             shouldReply,
124 |             strategy: this.name,
125 |             confidence,
126 |             waitTime: 3000, // 标准等待时间
127 |             reason: shouldReply ? "threshold_reached" : "threshold_not_reached",
128 |         };
129 |     }
130 | }
131 |
132 | // 对话流策略
133 | class ConversationFlowStrategy implements ReplyStrategy {
134 |     name = "conversation_flow";
135 |     enabled: boolean;
136 |
137 |     constructor(private config: ReplyConditionConfig["Strategies"]["ConversationFlow"], private flowAnalyzer: ConversationFlowAnalyzer) {
138 |         this.enabled = config.Enabled;
139 |     }
140 |
141 |     async evaluate(ctx: MessageContext): Promise<ReplyDecision> {
142 |         const channelId = ctx.koishiSession.channelId;
143 |         const userId = ctx.koishiSession.author.id;
144 |
145 |         // 构造消息对象
146 |         const message: ChatMessage = {
147 |             messageId: ctx.koishiSession.messageId,
148 |             content: ctx.koishiSession.content,
149 |             sender: {
150 |                 id: userId,
151 |                 name: ctx.koishiSession.author.name || ctx.koishiSession.author.nick,
152 |                 nick: ctx.koishiSession.author.nick,
153 |             },
154 |             timestamp: new Date(ctx.koishiSession.timestamp),
155 |             channel: { id: channelId, type: getChannelType(channelId) },
156 |         };
157 |
158 |         // 分析对话流
159 |         await this.flowAnalyzer.analyzeMessage(channelId, message);
160 |         const flowDecision = this.flowAnalyzer.shouldReply(channelId, message);
161 |
162 |         const shouldReply = flowDecision.shouldReply && flowDecision.confidence >= this.config.ConfidenceThreshold;
163 |
164 |         return {
165 |             shouldReply,
166 |             strategy: this.name,
167 |             confidence: flowDecision.confidence,
168 |             waitTime: flowDecision.suggestedWaitTime || 3000,
169 |             reason: flowDecision.reason,
170 |         };
171 |     }
172 | }
173 |
174 | // 简化的意愿值服务
175 | class WillingnessService {
176 |     private channelWillingness = new Map<string, number>();
177 |
178 |     constructor(private config?: ReplyConditionConfig["Advanced"]["Willingness"]) {}
179 |
180 |     updateWillingness(channelId: string, isMentioned: boolean, messageContent: string): void {
181 |         if (!this.config) return;
182 |
183 |         const current = this.channelWillingness.get(channelId) || 0;
184 |
185 |         // 基础增加量
186 |         let increase = isMentioned ? this.config.AtIncrease : this.config.MessageIncrease;
187 |
188 |         // 关键词检测 - 每匹配一个关键词增加额外意愿值
189 |         if (this.config.Keywords && messageContent) {
190 |             const lowerContent = messageContent.toLowerCase();
191 |             const matchedKeywords = this.config.Keywords.List.filter(keyword =>
192 |                 lowerContent.includes(keyword.toLowerCase())
193 |             );
194 |             if (matchedKeywords.length > 0) {
195 |                 increase += this.config.Keywords.Increase * matchedKeywords.length;
196 |             }
197 |         }
198 |
199 |         const newWillingness = Math.max(0, current + increase);
200 |         this.channelWillingness.set(channelId, newWillingness);
201 |
202 |     }
203 |
204 |     getWillingness(channelId: string): number {
205 |         return this.channelWillingness.get(channelId) || 0;
206 |     }
207 |
208 |     resetAfterReply(channelId: string): void {
209 |         if (!this.config) return;
210 |
211 |         const current = this.getWillingness(channelId);
212 |         const retained = current * this.config.RetentionAfterReply;
213 |         this.channelWillingness.set(channelId, retained);
214 |     }
215 |
216 |     decay(): void {
217 |         if (!this.config) return;
218 |
219 |         for (const [channelId, willingness] of this.channelWillingness) {
220 |             const decayed = Math.max(0, willingness - this.config.DecayRate);
221 |             this.channelWillingness.set(channelId, decayed);
222 |         }
223 |     }
224 | }
225 |
226 | export class CheckReplyCondition extends Middleware {
227 |     private strategies: ReplyStrategy[] = [];
228 |     private channelStates = new Map<string, ChannelState>();
229 |     private delayTimers = new Map<string, NodeJS.Timeout>();
230 |     private willingnessService: WillingnessService;
231 |     private decayTimer?: NodeJS.Timeout;
232 |
233 |     constructor(public ctx: Context, public config: ReplyConditionConfig) {
234 |         super("check-reply-condition", ctx, null, config);
235 |
236 |         this.willingnessService = new WillingnessService(config.Advanced?.Willingness);
237 |         this.initializeStrategies();
238 |         this.startDecayTimer();
239 |     }
240 |
241 |     private initializeStrategies(): void {
242 |         // 初始化@提及策略
243 |         if (this.config.Strategies.AtMention.Enabled) {
244 |             this.strategies.push(new AtMentionStrategy(this.config.Strategies.AtMention));
245 |         }
246 |
247 |         // 初始化阈值策略
248 |         if (this.config.Strategies.Threshold.Enabled) {
249 |             this.strategies.push(new ThresholdStrategy(this.config.Strategies.Threshold));
250 |         }
251 |
252 |         // 初始化对话流策略
253 |         if (this.config.Strategies.ConversationFlow.Enabled) {
254 |             const flowAnalyzer = new ConversationFlowAnalyzer(this.ctx);
255 |             this.strategies.push(new ConversationFlowStrategy(this.config.Strategies.ConversationFlow, flowAnalyzer));
256 |         }
257 |     }
258 |
259 |     private startDecayTimer(): void {
260 |         if (!this.config.Advanced?.Willingness) return;
261 |
262 |         // 每分钟衰减一次
263 |         this.decayTimer = setInterval(() => {
264 |             this.willingnessService.decay();
265 |         }, 60000);
266 |     }
267 |
268 |     async execute(ctx: MessageContext, next: () => Promise<void>): Promise<void> {
269 |         const channelId = ctx.koishiSession.channelId;
270 |         const userId = ctx.koishiSession.author.id;
271 |         const now = Date.now();
272 |
273 |         // 基础检查
274 |         if (ctx.koishiSession.author.isBot) return;
275 |         if (!this.isAllowedChannel(channelId)) return;
276 |         if (this.isChannelProcessing(channelId)) return;
277 |
278 |         // 获取或创建频道状态
279 |         const state = this.getOrCreateChannelState(channelId);
280 |
281 |         // 更新意愿值
282 |         this.willingnessService.updateWillingness(
283 |         	channelId,
284 |         	ctx.isMentioned,
285 |         	ctx.koishiSession.content
286 |         );
287 |         state.willingness = this.willingnessService.getWillingness(channelId);
288 |
289 |         // 检查是否需要取消现有定时器
290 |         if (this.shouldCancelExistingTimer(state, userId, now)) {
291 |             this.cancelExistingTimer(channelId);
292 |         } else if (this.delayTimers.has(channelId)) {
293 |             // 如果不取消且已有定时器，直接返回
294 |             return;
295 |         }
296 |
297 |         // 更新状态
298 |         state.lastMessageTime = now;
299 |         state.lastMessageUser = userId;
300 |
301 |         // 启动新的延迟处理
302 |         this.startDelayedProcessing(ctx, next, state);
303 |     }
304 |
305 |     private isAllowedChannel(channelId: string): boolean {
306 |         return this.config.Channels.some((slot) => slot.includes(channelId));
307 |     }
308 |
309 |     private isChannelProcessing(channelId: string): boolean {
310 |         const state = this.channelStates.get(channelId);
311 |         return state?.processing || false;
312 |     }
313 |
314 |     private getOrCreateChannelState(channelId: string): ChannelState {
315 |         let state = this.channelStates.get(channelId);
316 |         if (!state) {
317 |             state = {
318 |                 willingness: 0,
319 |                 processing: false,
320 |                 lastMessageTime: 0,
321 |                 lastMessageUser: "",
322 |             };
323 |             this.channelStates.set(channelId, state);
324 |         }
325 |         return state;
326 |     }
327 |
328 |     private shouldCancelExistingTimer(state: ChannelState, userId: string, now: number): boolean {
329 |         // 如果是同一用户在短时间内的连续消息，取消之前的定时器
330 |         return state.lastMessageUser === userId && now - state.lastMessageTime < this.config.Timing.SameUserThreshold;
331 |     }
332 |
333 |     private cancelExistingTimer(channelId: string): void {
334 |         const timer = this.delayTimers.get(channelId);
335 |         if (timer) {
336 |             clearTimeout(timer);
337 |             this.delayTimers.delete(channelId);
338 |         }
339 |     }
340 |
341 |     private startDelayedProcessing(ctx: MessageContext, next: () => Promise<void>, state: ChannelState): void {
342 |         const channelId = ctx.koishiSession.channelId;
343 |         const waitTime = this.config.Timing.WaitTime;
344 |
345 |         const timer = setTimeout(async () => {
346 |             try {
347 |                 await this.processMessage(ctx, next, state);
348 |             } catch (error) {
349 |                 state.processing = false;
350 |                 this.delayTimers.delete(channelId);
351 |
352 |                 // 将错误直接抛出，会传递到最终的错误处理
353 |                 throw error;
354 |             } finally {
355 |                 this.delayTimers.delete(channelId);
356 |             }
357 |         }, waitTime);
358 |
359 |         this.delayTimers.set(channelId, timer);
360 |     }
361 |
362 |     private async processMessage(ctx: MessageContext, next: () => Promise<void>, state: ChannelState): Promise<void> {
363 |         const channelId = ctx.koishiSession.channelId;
364 |
365 |         // 标记为处理中
366 |         state.processing = true;
367 |
368 |         try {
369 |             // 评估所有策略
370 |             const decisions = await Promise.all(
371 |                 this.strategies.filter((strategy) => strategy.enabled).map((strategy) => strategy.evaluate(ctx, state))
372 |             );
373 |
374 |             // 找到应该回复的决策
375 |             const positiveDecision = decisions.find((decision) => decision.shouldReply);
376 |             const shouldReply = !!positiveDecision || this.config.TestMode;
377 |
378 |             // 记录日志
379 |             this.logDecision(channelId, ctx.koishiSession.author.id, decisions, shouldReply);
380 |
381 |             if (shouldReply) {
382 |                 // 重置意愿值
383 |                 this.willingnessService.resetAfterReply(channelId);
384 |                 state.willingness = this.willingnessService.getWillingness(channelId);
385 |
386 |                 // 设置处理状态并继续中间件链
387 |                 ctx.state = ConversationState.PROCESSING;
388 |                 await next();
389 |             }
390 |         } finally {
391 |             state.processing = false;
392 |         }
393 |     }
394 |
395 | 	private logDecision(channelId: string, userId: string, decisions: ReplyDecision[], shouldReply: boolean): void {
396 | 		const strategyResults = decisions.map(d =>
397 | 			`${d.strategy}:${d.shouldReply ? '✓' : '✗'}${Math.round(d.confidence*100)}%`
398 | 		).join(', ');
399 |
400 | 		this.ctx.logger.info(
401 | 			`[CheckReplyCondition] 回复决策: ${channelId} | 用户:${userId} | 策略: ${strategyResults} | 结果: ${shouldReply ? '回复' : '不回复'}`
402 | 		);
403 | 	}
404 |
405 |     // 清理资源
406 |     public destroy(): void {
407 |         // 清理衰减定时器
408 |         if (this.decayTimer) {
409 |             clearInterval(this.decayTimer);
410 |             this.decayTimer = undefined;
411 |         }
412 |
413 |         // 清理延迟处理定时器
414 |         for (const timer of this.delayTimers.values()) {
415 |             clearTimeout(timer);
416 |         }
417 |         this.delayTimers.clear();
418 |     }
419 |
420 |     // 提供状态查询接口（用于调试）
421 |     public getChannelState(channelId: string): ChannelState | undefined {
422 |         return this.channelStates.get(channelId);
423 |     }
424 |
425 |     public getWillingness(channelId: string): number {
426 |         return this.willingnessService.getWillingness(channelId);
427 |     }
428 | }

</file_content>

<file_content path="external/YesImBot/packages/core/src/middleware/DatabaseStorage.ts">
 1 | import { Context, Element, h } from "koishi";
 2 | import { ScenarioManager } from "../services/scenario/ScenarioManager";
 3 | import { ChatMessage, MESSAGE_TABLE } from "../types/model";
 4 | import { getChannelType } from "../utils";
 5 | import { ImageProcessor } from "../utils/imageProcessor";
 6 | import { MessageContext, Middleware } from "./base";
 7 |
 8 | /**
 9 |  * 数据库存储中间件
10 |  */
11 | export class DatabaseStorageMiddleware extends Middleware {
12 |     constructor(protected ctx: Context, protected services: { imageProcessor: ImageProcessor; scenarioManager: ScenarioManager }) {
13 |         super("database-storage", ctx, services, null);
14 |     }
15 |
16 |     async execute(ctx: MessageContext, next: () => Promise<void>): Promise<void> {
17 |         const elements = ctx.koishiSession.elements;
18 |         const processedElements: Element[] = [];
19 |         for await (const element of elements) {
20 |             switch (element.type) {
21 |                 case "text":
22 |                     processedElements.push(element);
23 |                     break;
24 |                 case "image":
25 |                 case "img":
26 |                     const imageData = await this.services.imageProcessor.process(element);
27 |                     if (imageData) {
28 |                         processedElements.push(
29 |                             h("img", { id: imageData.id, summary: element.attrs.summary, desc: imageData.desc || null })
30 |                         );
31 |                     } else {
32 |                         processedElements.push(element);
33 |                     }
34 |                     break;
35 |                 case "at":
36 |                     processedElements.push(element);
37 |                     break;
38 |                 case "video":
39 |                     processedElements.push(element);
40 |                     break;
41 |                 default:
42 |                     processedElements.push(element);
43 |                     break;
44 |             }
45 |         }
46 |
47 |         if (ctx.koishiSession.quote) processedElements.unshift(h.quote(ctx.koishiSession.quote.id));
48 |
49 |         const content = processedElements.join("");
50 |
51 |         // 保存接收到的消息
52 |         const newMessage = await this.saveReceivedMessage(ctx, content);
53 |
54 |         // 如果消息成功保存，则通知 ScenarioManager 更新缓存中的 Scenario 实例
55 |         if (newMessage) {
56 |             await this.services.scenarioManager.updateMessage(newMessage, ctx.koishiSession, true);
57 |         }
58 |
59 |         // 继续处理链
60 |         await next();
61 |     }
62 |
63 |     private async saveReceivedMessage(ctx: MessageContext, content: string): Promise<ChatMessage | null> {
64 |         const session = ctx.koishiSession;
65 |         // 检查消息是否已存在，防止重复存储
66 |         const messages = await this.ctx.database.get(MESSAGE_TABLE, {
67 |             messageId: session.messageId,
68 |             channel: {
69 |                 id: session.channelId,
70 |             },
71 |         });
72 |         if (messages.length === 0) {
73 |             const message: ChatMessage = {
74 |                 messageId: session.messageId,
75 |                 content: content,
76 |                 sender: {
77 |                     ...session.author,
78 |                     id: session.author.id,
79 |                     name: session.author.name,
80 |                     nick: session.author.nick,
81 |                 },
82 |                 channel: {
83 |                     id: session.channelId,
84 |                     type: getChannelType(session.channelId),
85 |                 },
86 |                 timestamp: new Date(session.timestamp),
87 |             };
88 |             await this.ctx.database.create(MESSAGE_TABLE, message);
89 | 			this.ctx.logger.info(`[DB] [${session.channelId}] [${session.author.id}] 收到消息: ${content.substring(0, 50)}${content.length > 50 ? "..." : ""}`);
90 |
91 |             return message;
92 |         }
93 |         return null;
94 |     }
95 | }

</file_content>

<file_content path="external/YesImBot/packages/core/src/middleware/ErrorHandling.ts">
  1 | import { Context, Session } from "koishi";
  2 | import * as os from "os";
  3 | import { v4 as uuidv4 } from "uuid";
  4 | import type { GenerateTextResult } from "xsai";
  5 | import { Scenario } from "../services/scenario/Scenario";
  6 | import { MessageContext, Middleware } from "./base";
  7 |
  8 | // 1. 优化 ErrorContext 接口，增加原始错误对象和更多上下文
  9 | export interface ErrorReportContext {
 10 |     originalError: Error; // 包含原始错误对象，方便提取更多信息
 11 |     scenario?: Scenario;
 12 |     llmResponse?: GenerateTextResult;
 13 |     koishiContext?: Context; // Koishi 上下文对象
 14 |     koishiSession?: Session; // Koishi 会话对象
 15 |     additionalInfo?: Record<string, any>; // 额外的自定义信息
 16 |     errorId?: string; // 错误唯一ID
 17 | }
 18 |
 19 | export interface ErrorHandlingOptions {
 20 |     debug?: boolean; // 是否开启调试模式，打印详细堆栈
 21 |     uploadDump?: boolean; // 是否上传错误倾倒
 22 |     pasteServiceUrl?: string; // 粘贴服务的URL
 23 |     // 敏感信息处理选项，例如是否包含完整的会话内容
 24 |     includeFullSessionContent?: boolean;
 25 | }
 26 |
 27 | export class ErrorHandlingMiddleware extends Middleware {
 28 |     name = "error-handling";
 29 |
 30 |     // 2. 优化构造函数，使用更清晰的选项接口
 31 |     constructor(ctx: Context, config: ErrorHandlingOptions) {
 32 |         super("error-handling", ctx, null, config);
 33 |     }
 34 |
 35 |     async execute(ctx: MessageContext, next: () => Promise<void>): Promise<void> {
 36 |         const logger = this.ctx.logger;
 37 |         try {
 38 |             await next(); // 执行后续中间件
 39 |         } catch (error) {
 40 |             const errorId = uuidv4(); // 为每个错误生成一个唯一ID
 41 |
 42 |             // 3. 改进本地日志记录，更详细且带有错误ID
 43 |             logger.error(`[Error ID: ${errorId}] 发生未知错误，已跳过回复。`);
 44 |             logger.error(`Error Type: ${(error as Error).name}`);
 45 |             logger.error(`Error Message: ${(error as Error).message}`);
 46 |
 47 |             if (this.config.debug) {
 48 |                 logger.error(`Error Stack:`, (error as Error).stack);
 49 |             } else {
 50 |                 logger.error(`For detailed stack trace, enable debug mode.`);
 51 |             }
 52 |
 53 |             // 记录触发错误的用户和频道信息
 54 |             if (ctx.koishiSession) {
 55 |                 logger.error(
 56 |                     `Triggered by User: ${ctx.koishiSession.userId} (${ctx.koishiSession.platform}) in Channel: ${ctx.koishiSession.channelId}`
 57 |                 );
 58 |             }
 59 |
 60 |             try {
 61 |                 if (this.config.uploadDump) {
 62 |                     const errorDump = await this.formatErrorDump(error as Error, {
 63 |                         originalError: error as Error,
 64 |                         scenario: ctx.currentScenario,
 65 |                         llmResponse: ctx.llmResponse,
 66 |                         koishiSession: ctx.koishiSession,
 67 |                         koishiContext: ctx.koishiContext,
 68 |                         // additionalInfo: ctx.additionalInfo, // 如果 MessageContext 有此字段
 69 |                         errorId: errorId,
 70 |                     });
 71 |
 72 |                     const pasteUrl = await this.uploadToPaste(errorDump);
 73 |                     if (pasteUrl) {
 74 |                         logger.info(`[Error ID: ${errorId}] Error dump uploaded to: ${pasteUrl}`);
 75 |                         // 4. 可以考虑在这里向用户发送一个友好的提示，告知问题已被记录
 76 |                         // 例如：ctx.koishiSession?.send('抱歉，程序遇到了一些问题，我们已记录并会尽快处理。');
 77 |                     }
 78 |                 }
 79 |             } catch (uploadError) {
 80 |                 logger.error(`[Error ID: ${errorId}] Error uploading error dump:`, (uploadError as Error).message);
 81 |                 if (this.config.debug) {
 82 |                     logger.error(`Upload error stack:`, (uploadError as Error).stack);
 83 |                 }
 84 |             }
 85 |         }
 86 |     }
 87 |
 88 |     private async uploadToPaste(content: string): Promise<string | null> {
 89 |         const logger = this.ctx.logger;
 90 |         if (!this.config.pasteServiceUrl) {
 91 |             logger.warn("No paste service URL configured. Skipping dump upload.");
 92 |             return null;
 93 |         }
 94 |
 95 |         try {
 96 |             const formData = new FormData();
 97 |             formData.append("c", content);
 98 |
 99 |             const response = await fetch(this.config.pasteServiceUrl, {
100 |                 method: "POST",
101 |                 body: formData,
102 |             });
103 |
104 |             const data = await response.json();
105 |
106 |             if (data && data.url) {
107 |                 return data.url;
108 |             } else {
109 |                 logger.error(
110 |                     `Failed to upload to paste service (${this.config.pasteServiceUrl}):`,
111 |                     data.error || `Status: ${response.status} - ${response.statusText}`
112 |                 );
113 |                 return null;
114 |             }
115 |         } catch (error) {
116 |             logger.error(`Error connecting to dump host (${this.config.pasteServiceUrl}):`, (error as Error).message);
117 |             return null;
118 |         }
119 |     }
120 |
121 |     // 5. 极大地美化 formatErrorDump 方法
122 |     private async formatErrorDump(error: Error, context: ErrorReportContext): Promise<string> {
123 |         const dumpSections: string[] = [];
124 |
125 |         const packageJson = require("../../package.json");
126 |
127 |         // --- Header ---
128 |         dumpSections.push(
129 |             `# YesImBot Error Report\n`,
130 |             `**Error ID:** \`${context.errorId || "N/A"}\`\n`,
131 |             `**Timestamp (UTC):** \`${new Date().toISOString()}\`\n`,
132 |             `**Plugin Version:** \`${packageJson.version}\`\n`,
133 |             `---`
134 |         );
135 |
136 |         // --- Error Details ---
137 |         dumpSections.push(`## 🔴 Error Details\n`, `**Type:** \`${error.name}\`\n`, `**Message:** \`${error.message}\`\n`);
138 |
139 |         if (error.stack) {
140 |             dumpSections.push(`### Stack Trace:\n`, `\`\`\`typescript\n${error.stack}\n\`\`\``);
141 |         }
142 |
143 |         // --- System Information ---
144 |         dumpSections.push(
145 |             `\n---\n`,
146 |             `## ⚙️ System Information\n`,
147 |             `**Node.js Version:** \`${process.version}\`\n`,
148 |             `**Platform:** \`${process.platform} (${os.release()})\`\n`,
149 |             `**Architecture:** \`${process.arch}\`\n`,
150 |             `**CPU Cores:** \`${os.cpus().length}\`\n`,
151 |             `**Total Memory:** \`${(os.totalmem() / 1024 ** 3).toFixed(2)} GB\`\n`
152 |         );
153 |
154 |         // --- Session Context ---
155 |         if (context.koishiSession) {
156 |             const session = context.koishiSession;
157 |             dumpSections.push(
158 |                 `\n---\n`,
159 |                 `## 👥 Session Context\n`,
160 |                 `**Platform:** \`${session.platform}\`\n`,
161 |                 `**User ID:** \`${session.userId}\`\n`,
162 |                 `**Channel ID:** \`${session.channelId}\`\n`,
163 |                 `**Guild ID:** \`${session.guildId || "N/A"}\`\n`,
164 |                 `**Self ID:** \`${session.selfId}\`\n`,
165 |                 `**Message ID:** \`${session.messageId || "N/A"}\`\n`
166 |             );
167 |
168 |             // 谨慎包含原始消息内容，考虑敏感信息
169 |             if (this.config.includeFullSessionContent && session.content) {
170 |                 dumpSections.push(`**Original Message Content (potentially sensitive):**\n`, `\`\`\`text\n${session.content}\n\`\`\``);
171 |             } else if (session.content) {
172 |                 dumpSections.push(
173 |                     `**Original Message Content (first 100 chars, truncated):**\n`,
174 |                     `\`\`\`text\n${session.content.substring(0, 100)}${session.content.length > 100 ? "..." : ""}\n\`\`\``
175 |                 );
176 |             }
177 |         }
178 |
179 |         // --- Scenario Context ---
180 |         if (context.scenario) {
181 |             dumpSections.push(`\n---\n`, `## 📜 Scenario Context\n`);
182 |             // 检查 Scenario 是否有 render 方法
183 |             if (context.scenario instanceof Scenario && typeof (context.scenario as Scenario).render === "function") {
184 |                 try {
185 |                     const scenarioContext = JSON.stringify(await context.scenario.render(), null, 2);
186 |                     dumpSections.push(`\`\`\`markdown\n${scenarioContext}\n\`\`\``);
187 |                 } catch (e) {
188 |                     dumpSections.push(
189 |                         `*Failed to render scenario: ${(e as Error).message}*\n\`\`\`json\n${JSON.stringify(
190 |                             context.scenario,
191 |                             null,
192 |                             2
193 |                         )}\n\`\`\``
194 |                     );
195 |                 }
196 |             } else {
197 |                 dumpSections.push(`\`\`\`json\n${JSON.stringify(context.scenario, null, 2)}\n\`\`\``);
198 |             }
199 |         }
200 |
201 |         // --- LLM Response ---
202 |         if (context.llmResponse) {
203 |             dumpSections.push(`\n---\n`, `## 🤖 LLM Response\n`);
204 |             if (typeof context.llmResponse?.text === "string") {
205 |                 dumpSections.push(`\`\`\`text\n${context.llmResponse.text}\n\`\`\``);
206 |             }
207 |             dumpSections.push(`\n---\n`, `## Raw Response (if available)\n`);
208 |             dumpSections.push(`\`\`\`json\n${JSON.stringify(context.llmResponse, null, 2)}\n\`\`\``);
209 |         }
210 |
211 |         // --- Additional Info ---
212 |         if (context.additionalInfo && Object.keys(context.additionalInfo).length > 0) {
213 |             dumpSections.push(
214 |                 `\n---\n`,
215 |                 `## ➕ Additional Information\n`,
216 |                 `\`\`\`json\n${JSON.stringify(context.additionalInfo, null, 2)}\n\`\`\``
217 |             );
218 |         }
219 |
220 |         // --- Footer ---
221 |         dumpSections.push(`\n---\n`, `*This report is generated by YesImBot's error handling middleware.*`);
222 |
223 |         return dumpSections.join("\n");
224 |     }
225 | }

</file_content>

<file_content path="external/YesImBot/packages/core/src/middleware/LLMProcessing.ts">
  1 | import { Context, sleep } from "koishi";
  2 | import { ChatModelSwitcher } from "../adapters";
  3 | import { LLMAdapterError, LLMRequestError, LLMRetryExhaustedError, LLMTimeoutError } from "../errors";
  4 | import { PromptBuilder } from "../prompt/PromptBuilder";
  5 | import { ScenarioManager } from "../services/scenario/ScenarioManager";
  6 | import { ConversationState, MessageContext, Middleware } from "./base";
  7 |
  8 | /**
  9 |  * 重试配置
 10 |  */
 11 | export interface RetryConfig {
 12 |     maxRetries: number;
 13 |     timeoutMs: number;
 14 |     retryDelayMs: number;
 15 |     exponentialBackoff: boolean;
 16 |     retryableErrors: string[];
 17 | }
 18 |
 19 | /**
 20 |  * 适配器切换配置
 21 |  */
 22 | export interface AdapterSwitchingConfig {
 23 |     enabled: boolean;
 24 |     maxAttempts: number;
 25 | }
 26 |
 27 | /**
 28 |  * LLM重试管理器
 29 |  * 负责处理单个适配器的重试逻辑，包括超时控制和指数退避
 30 |  */
 31 | class LLMRetryManager {
 32 |     private readonly logger: any;
 33 |
 34 |     constructor(private retryConfig: RetryConfig, private adapterConfig: AdapterSwitchingConfig, baseLogger: any) {
 35 |         this.logger = baseLogger("LLMRetryManager");
 36 |     }
 37 |
 38 |     /**
 39 |      * 执行带重试的LLM请求
 40 |      */
 41 |     async executeWithRetry<T>(
 42 |         operation: (abortSignal: AbortSignal, cancelTimeout: () => void) => Promise<T>,
 43 |         adapterName: string
 44 |     ): Promise<T> {
 45 |         let lastError: Error | null = null;
 46 |
 47 |         for (let attempt = 0; attempt <= this.retryConfig.maxRetries; attempt++) {
 48 |             // 为每次请求创建独立的AbortController
 49 |             const controller = new AbortController();
 50 |             let timeoutId: NodeJS.Timeout | null = setTimeout(() => controller.abort(), this.retryConfig.timeoutMs);
 51 |
 52 |             // 提供取消定时器的回调函数
 53 |             const cancelTimeout = () => {
 54 |                 if (timeoutId) {
 55 |                     clearTimeout(timeoutId);
 56 |                     timeoutId = null;
 57 |                 }
 58 |             };
 59 |
 60 |             try {
 61 |                 this.logger.debug(`尝试请求 ${adapterName}，第 ${attempt + 1} 次`);
 62 |
 63 |                 const result = await operation(controller.signal, cancelTimeout);
 64 |                 cancelTimeout(); // 确保定时器被清除
 65 |
 66 |                 if (attempt > 0) {
 67 |                     this.logger.info(`${adapterName} 重试成功，共尝试 ${attempt + 1} 次`);
 68 |                 }
 69 |
 70 |                 return result;
 71 |             } catch (error: any) {
 72 |                 cancelTimeout(); // 确保定时器被清除
 73 |                 lastError = error;
 74 |
 75 |                 // 检查是否是中止错误
 76 |                 if (error?.name === "AbortError") {
 77 |                     if (controller.signal.aborted) {
 78 |                         // 超时中止
 79 |                         const timeoutError = new LLMTimeoutError(
 80 |                             `请求超时 (${this.retryConfig.timeoutMs}ms)`,
 81 |                             this.retryConfig.timeoutMs,
 82 |                             adapterName
 83 |                         );
 84 |
 85 |                         if (!this.isRetryableError(timeoutError)) {
 86 |                             throw timeoutError;
 87 |                         }
 88 |                         lastError = timeoutError;
 89 |                     } else {
 90 |                         // 外部中止，不重试
 91 |                         throw error;
 92 |                     }
 93 |                 }
 94 |
 95 |                 // 检查是否可重试
 96 |                 if (!this.isRetryableError(error)) {
 97 |                     this.logger.warn(`${adapterName} 遇到不可重试错误: ${error.message}`);
 98 |                     throw new LLMRequestError(error.message, adapterName, attempt, false, {}, error);
 99 |                 }
100 |
101 |                 // 如果还有重试机会，等待后重试
102 |                 if (attempt < this.retryConfig.maxRetries) {
103 |                     const delay = this.calculateRetryDelay(attempt);
104 |                     this.logger.warn(
105 |                         `${adapterName} 请求失败 (${error.message})，${delay}ms 后重试 (${attempt + 1}/${this.retryConfig.maxRetries})`
106 |                     );
107 |                     await sleep(delay);
108 |                 } else {
109 |                     this.logger.error(`${adapterName} 重试耗尽，共尝试 ${attempt + 1} 次，最后错误: ${error.message}`);
110 |                 }
111 |             }
112 |         }
113 |
114 |         // 所有重试都失败了
115 |         throw new LLMRequestError(
116 |             `重试耗尽: ${lastError?.message || "未知错误"}`,
117 |             adapterName,
118 |             this.retryConfig.maxRetries,
119 |             true,
120 |             {},
121 |             lastError || undefined
122 |         );
123 |     }
124 |
125 |     /**
126 |      * 判断错误是否可重试
127 |      */
128 |     private isRetryableError(error: any): boolean {
129 |         if (!error) return false;
130 |
131 |         // 检查错误名称
132 |         if (this.retryConfig.retryableErrors.includes(error.name)) {
133 |             return true;
134 |         }
135 |
136 |         // 检查错误代码（网络错误）
137 |         if (error.cause?.code && this.retryConfig.retryableErrors.includes(error.cause.code)) {
138 |             return true;
139 |         }
140 |
141 |         // 检查特定的错误消息模式
142 |         if (error.message) {
143 |             const message = error.message.toLowerCase();
144 |             if (
145 |                 message.includes("fetch failed") ||
146 |                 message.includes("network error") ||
147 |                 message.includes("timeout") ||
148 |                 message.includes("连接") ||
149 |                 message.includes("超时")
150 |             ) {
151 |                 return true;
152 |             }
153 |         }
154 |
155 |         return false;
156 |     }
157 |
158 |     /**
159 |      * 计算重试延迟
160 |      */
161 |     private calculateRetryDelay(attempt: number): number {
162 |         if (!this.retryConfig.exponentialBackoff) {
163 |             return this.retryConfig.retryDelayMs;
164 |         }
165 |
166 |         // 指数退避：base * 2^attempt，加上随机抖动
167 |         const exponentialDelay = this.retryConfig.retryDelayMs * Math.pow(2, attempt);
168 |         const jitter = Math.random() * 0.3 * exponentialDelay; // 30% 抖动
169 |         return Math.min(exponentialDelay + jitter, 30000); // 最大30秒
170 |     }
171 | }
172 |
173 | /**
174 |  * LLM适配器管理器
175 |  * 负责在多个适配器之间切换，实现故障转移
176 |  */
177 | class LLMAdapterManager {
178 |     private failedAdapters: Set<string> = new Set();
179 |     private currentIndex = 0;
180 |     private readonly logger: any;
181 |
182 |     constructor(private chatModelSwitcher: ChatModelSwitcher, private adapterConfig: AdapterSwitchingConfig, baseLogger: any) {
183 |         this.logger = baseLogger("LLMAdapterManager");
184 |     }
185 |
186 |     /**
187 |      * 执行带适配器切换的LLM请求
188 |      */
189 |     async executeWithAdapterSwitching<T>(operation: (adapterName: string, model: any) => Promise<T>): Promise<T> {
190 |         if (!this.adapterConfig.enabled) {
191 |             // 不启用适配器切换，直接使用当前适配器
192 |             const model = this.chatModelSwitcher.getCurrent();
193 |             if (!model) {
194 |                 throw new LLMAdapterError("没有可用的LLM适配器", "unknown", 0);
195 |             }
196 |             return await operation("current", model);
197 |         }
198 |
199 |         const totalAdapters = this.chatModelSwitcher.length;
200 |         let attempt = 0;
201 |         let lastError: Error | null = null;
202 |
203 |         // 重置失败的适配器记录（每次新请求时）
204 |         this.failedAdapters.clear();
205 |         this.currentIndex = 0;
206 |
207 |         while (attempt < this.adapterConfig.maxAttempts && attempt < totalAdapters) {
208 |             // 尝试当前索引的适配器
209 |             let model = this.chatModelSwitcher.getCurrent();
210 |
211 |             // 如果当前适配器不可用，尝试切换
212 |             if (!model && attempt < totalAdapters - 1) {
213 |                 this.switchToNextAdapter();
214 |                 model = this.chatModelSwitcher.getCurrent();
215 |             }
216 |
217 |             if (!model) {
218 |                 throw new LLMAdapterError("没有可用的LLM适配器", "unknown", totalAdapters);
219 |             }
220 |
221 |             const adapterName = this.getAdapterName(model);
222 |
223 |             // 跳过已经失败的适配器（在同一次请求中）
224 |             if (this.failedAdapters.has(adapterName)) {
225 |                 if (attempt < totalAdapters - 1) {
226 |                     this.switchToNextAdapter();
227 |                 }
228 |                 attempt++;
229 |                 continue;
230 |             }
231 |
232 |             try {
233 |                 this.logger.debug(`使用适配器: ${adapterName} (尝试 ${attempt + 1}/${this.adapterConfig.maxAttempts})`);
234 |                 const result = await operation(adapterName, model);
235 |
236 |                 if (attempt > 0) {
237 |                     this.logger.info(`适配器切换成功，使用 ${adapterName}`);
238 |                 }
239 |
240 |                 this.switchToNextAdapter();
241 |
242 |                 return result;
243 |             } catch (error: any) {
244 |                 lastError = error;
245 |                 this.failedAdapters.add(adapterName);
246 |
247 |                 // 如果是不可重试的错误，直接抛出
248 |                 if (error instanceof LLMRequestError && !error.isRetryable) {
249 |                     throw error;
250 |                 }
251 |
252 |                 this.logger.warn(`适配器 ${adapterName} 失败: ${error.message}`);
253 |
254 |                 // 切换到下一个适配器
255 |                 if (attempt < this.adapterConfig.maxAttempts - 1 && attempt < totalAdapters - 1) {
256 |                     this.switchToNextAdapter();
257 |                     this.logger.info(`切换到下一个适配器，剩余尝试次数: ${this.adapterConfig.maxAttempts - attempt - 1}`);
258 |                 }
259 |
260 |                 attempt++;
261 |             }
262 |         }
263 |
264 |         // 所有适配器都失败了
265 |         throw new LLMRetryExhaustedError(`所有LLM适配器都失败了`, attempt, Array.from(this.failedAdapters), lastError || undefined);
266 |     }
267 |
268 |     /**
269 |      * 切换到下一个适配器
270 |      */
271 |     private switchToNextAdapter(): void {
272 |         this.currentIndex = (this.currentIndex + 1) % this.chatModelSwitcher.length;
273 |         this.chatModelSwitcher.switchToNext();
274 |     }
275 |
276 |     /**
277 |      * 获取适配器名称
278 |      */
279 |     private getAdapterName(model: any): string {
280 |         return model?.constructor?.name || model?.name || "unknown";
281 |     }
282 | }
283 |
284 | export class LLMProcessingMiddleware extends Middleware {
285 |     private retryManager: LLMRetryManager;
286 |     private adapterManager: LLMAdapterManager;
287 |     private readonly logger: any;
288 |
289 |     constructor(
290 |         protected ctx: Context,
291 |         protected services: {
292 |             readonly scenarioManager: ScenarioManager;
293 |             readonly chatModelSwitcher: ChatModelSwitcher;
294 |             readonly promptBuilder: PromptBuilder;
295 |         },
296 |         protected config: {
297 |             debug?: boolean;
298 |             retryConfig?: RetryConfig;
299 |             adapterSwitchingConfig?: AdapterSwitchingConfig;
300 |         }
301 |     ) {
302 |         super("llm-processing", ctx, services, config);
303 |
304 |         // 创建带前缀的logger
305 |         this.logger = this.ctx.logger("LLMProcessing");
306 |
307 |         // 使用默认配置
308 |         const defaultRetryConfig: RetryConfig = {
309 |             maxRetries: 3,
310 |             timeoutMs: 30000,
311 |             retryDelayMs: 1000,
312 |             exponentialBackoff: true,
313 |             retryableErrors: [
314 |                 "ECONNREFUSED",
315 |                 "ECONNRESET",
316 |                 "ETIMEDOUT",
317 |                 "ENOTFOUND",
318 |                 "EPIPE",
319 |                 "XSAIError",
320 |                 "NetworkError",
321 |                 "TimeoutError",
322 |                 "AbortError",
323 |             ],
324 |         };
325 |
326 |         const defaultAdapterConfig: AdapterSwitchingConfig = {
327 |             enabled: true,
328 |             maxAttempts: 3,
329 |         };
330 |
331 |         this.retryManager = new LLMRetryManager(
332 |             config.retryConfig || defaultRetryConfig,
333 |             config.adapterSwitchingConfig || defaultAdapterConfig,
334 |             this.ctx.logger
335 |         );
336 |
337 |         this.adapterManager = new LLMAdapterManager(
338 |             this.services.chatModelSwitcher,
339 |             config.adapterSwitchingConfig || defaultAdapterConfig,
340 |             this.ctx.logger
341 |         );
342 |     }
343 |
344 |     async execute(ctx: MessageContext, next: () => Promise<void>): Promise<void> {
345 |         if (ctx.state !== ConversationState.PROCESSING) {
346 |             return await next();
347 |         }
348 |
349 |         try {
350 |             ctx.currentScenario = await this.services.scenarioManager.getScenario(ctx.koishiSession);
351 |
352 |             // 处理所有与该频道相关的交互记录的生命周期
353 |             await this.services.scenarioManager.processInteractions(ctx.koishiSession.channelId);
354 |
355 |             // 构建提示词
356 |             const systemPrompt = await this.services.promptBuilder.buildSystemPrompt(ctx);
357 |             const userPrompt = await this.services.promptBuilder.buildUserPrompt(ctx);
358 |
359 |             if (this.config.debug) {
360 |                 this.logger.debug("--- LLM System Prompt ---");
361 |                 this.logger.debug(systemPrompt);
362 |                 this.logger.debug("--- LLM User Prompt ---");
363 |                 this.logger.debug(userPrompt);
364 |                 this.logger.debug("--- End Prompts ---");
365 |             }
366 |
367 |             // 执行LLM请求（带适配器切换和重试）
368 |             ctx.llmResponse = await this.adapterManager.executeWithAdapterSwitching(async (adapterName: string, model: any) => {
369 |                 return await this.retryManager.executeWithRetry(async (abortSignal: AbortSignal, cancelTimeout: () => void) => {
370 |                     return await model.chat(
371 |                         [
372 |                             { role: "system", content: systemPrompt },
373 |                             { role: "user", content: userPrompt },
374 |                         ],
375 |                         null,
376 |                         {
377 |                             debug: this.config.debug,
378 |                             logger: ctx.koishiContext.logger,
379 |                             abortSignal,
380 |                             onStreamStart: cancelTimeout, // 当流式响应开始时取消定时器
381 |                         }
382 |                     );
383 |                 }, adapterName);
384 |             });
385 |
386 |             await ctx.transitionTo(ConversationState.RESPONDING);
387 |             await next();
388 |
389 |             // LLM成功响应后的清理工作
390 |             await this.services.scenarioManager.setLastReplyTime(ctx.koishiSession.channelId);
391 |             ctx.currentScenario.clearPendingMessages();
392 |         } catch (error: any) {
393 |             // 处理不同类型的错误
394 |             if (error instanceof LLMRetryExhaustedError) {
395 |                 this.logger.error(error.toUserMessage());
396 |                 this.logger.error(`错误详情: ${error.toLogFormat()}`);
397 |             } else if (error instanceof LLMTimeoutError) {
398 |                 this.logger.warn(error.toUserMessage());
399 |             } else if (error instanceof LLMRequestError) {
400 |                 this.logger.warn(error.toUserMessage());
401 |             } else if (error?.name === "AbortError") {
402 |                 // 外部中止请求，静默处理
403 |                 this.logger.info("请求被外部中止");
404 |                 return;
405 |             } else {
406 |                 // 其他未知错误
407 |                 this.logger.error(`未预期的错误: ${error?.message || "未知错误"}`);
408 |             }
409 |
410 |             throw error;
411 |         }
412 |     }
413 | }

</file_content>

<file_content path="external/YesImBot/packages/core/src/middleware/ResponseHandling.ts">
  1 | import { Context, Logger, Random } from "koishi";
  2 | import { Failed, ToolCallResult } from "../extensions";
  3 | import { ScenarioManager } from "../services/scenario/ScenarioManager";
  4 | import { Interaction, INTERACTION_TABLE } from "../types/model";
  5 | import { extractJSONFromString } from "../utils/parse-structured-output";
  6 | import { ConversationState, MessageContext, Middleware, MiddlewareManager } from "./base";
  7 |
  8 | interface FunctionTool {
  9 |     function: string;
 10 |     params: Record<string, unknown>;
 11 | }
 12 |
 13 | interface OutputFormat {
 14 |     thoughts: {
 15 |         observe: string;
 16 |         analyze_infer: string;
 17 |         plan: string;
 18 |     };
 19 |     actions: FunctionTool[];
 20 |     request_heartbeat: boolean;
 21 | }
 22 |
 23 | export class ResponseHandlingMiddleware extends Middleware {
 24 |     // 默认配置常量
 25 |     private static readonly DEFAULT_MAX_RETRY = 3;
 26 |     private static readonly DEFAULT_LIFE = 3;
 27 |     private static readonly DEFAULT_MAX_HEARTBEAT = 5;
 28 |     private static readonly RETRY_DELAY_MS = 1500; // 重试延迟
 29 |
 30 |     private readonly logger: Logger;
 31 |
 32 |     constructor(
 33 |         protected ctx: Context,
 34 |         protected services: {
 35 |             readonly scenarioManager: ScenarioManager;
 36 |             readonly middlewareManager: MiddlewareManager;
 37 |         },
 38 |         protected config: {
 39 |             maxRetry: number;
 40 |             life: number;
 41 |             maxHeartbeat?: number;
 42 |         }
 43 |     ) {
 44 |         super("response-handling", ctx, services, config);
 45 |         // 为该中间件创建一个带命名空间的 logger
 46 |         this.logger = ctx.logger("ResponseHandling");
 47 |     }
 48 |
 49 |     /**
 50 |      * 中间件主执行函数
 51 |      */
 52 |     async execute(ctx: MessageContext, next: () => Promise<void>): Promise<void> {
 53 |         if (ctx.state !== ConversationState.RESPONDING) {
 54 |             return next();
 55 |         }
 56 |
 57 |         try {
 58 |             const response = this._parseAndValidateResponse(ctx.llmResponse.text);
 59 |             if (!response) {
 60 |                 this.logger.warn("LLM 响应解析失败或无效，处理中止。");
 61 |                 await this._finalizeProcessing(ctx);
 62 |                 return;
 63 |             }
 64 |
 65 |             this._logThoughts(response.thoughts);
 66 |             await this._processActions(ctx, response.actions);
 67 |
 68 |             if (response.request_heartbeat) {
 69 |                 await this._handleHeartbeat(ctx);
 70 |             } else {
 71 |                 await next();
 72 |                 await this._finalizeProcessing(ctx);
 73 |             }
 74 |         } catch (error) {
 75 |             this.logger.error("在处理LLM响应时发生未知错误: %s", error.message);
 76 |             this.logger.error(error.stack);
 77 |             await this._finalizeProcessing(ctx); // 保证即使出错也能释放频道
 78 |         }
 79 |     }
 80 |
 81 |     /**
 82 |      * 解析并验证 LLM 的 JSON 响应。
 83 |      * @returns 解析后的数据，如果无效则返回 null。
 84 |      */
 85 |     private _parseAndValidateResponse(text: string): OutputFormat | null {
 86 |         try {
 87 |             const jsonObjects = this._extractJson(text);
 88 |             if (!jsonObjects || jsonObjects.length === 0) {
 89 |                 throw new Error("响应中未找到有效的 JSON 内容。");
 90 |             }
 91 |
 92 |             // 通常我们只关心第一个有效的结构化输出
 93 |             const response = jsonObjects[0] as OutputFormat;
 94 |
 95 |             // 基本的结构验证
 96 |             if (!response.thoughts || !response.actions) {
 97 |                 throw new Error("JSON 结构缺少 'thoughts' 或 'actions' 字段。");
 98 |             }
 99 |             for (const action of response.actions) {
100 |                 if (!action.function || typeof action.params !== "object") {
101 |                     throw new Error("Action 格式错误，必须包含 'function' 和 'params'。");
102 |                 }
103 |             }
104 |             return response;
105 |         } catch (error) {
106 |             this.logger.warn(`[解析失败] ${error.message}`);
107 |             return null;
108 |         }
109 |     }
110 |
111 |     /**
112 |      * 美化并输出模型的思考过程。
113 |      */
114 |     private _logThoughts(thoughts: OutputFormat["thoughts"]): void {
115 |         this.logger.info("🤔 LLM 思考过程分析:");
116 |         this.logger.info(`  - 观察 (Observe): ${thoughts.observe}`);
117 |         this.logger.info(`  - 推理 (Analyze): ${thoughts.analyze_infer}`);
118 |         this.logger.info(`  - 计划 (Plan):    ${thoughts.plan}`);
119 |     }
120 |
121 |     /**
122 |      * 循环处理所有工具调用。
123 |      */
124 |     private async _processActions(ctx: MessageContext, actions: FunctionTool[]): Promise<void> {
125 |         for (const action of actions) {
126 |             await this.recordToolCall(ctx, action.function, action.params);
127 |
128 |             const result = await this.executeToolCall(ctx, action.function, action.params);
129 |
130 |             await this.recordToolResult(ctx, action.function, result);
131 |         }
132 |     }
133 |
134 |     /**
135 |      * 处理连续对话（Heartbeat）逻辑。
136 |      */
137 |     private async _handleHeartbeat(ctx: MessageContext): Promise<void> {
138 |         const maxHeartbeat = this.config.maxHeartbeat ?? ResponseHandlingMiddleware.DEFAULT_MAX_HEARTBEAT;
139 |         if (ctx.heartbeatCount >= maxHeartbeat) {
140 |             this.logger.warn(`❤️ Heartbeat 已达到最大限制 (${maxHeartbeat})，对话强制结束。`);
141 |             await this._finalizeProcessing(ctx);
142 |             return;
143 |         }
144 |
145 |         ctx.heartbeatCount++;
146 |         this.logger.info(`❤️ 触发 Heartbeat，准备进行第 ${ctx.heartbeatCount} 次连续对话...`);
147 |
148 |         await ctx.transitionTo(ConversationState.PROCESSING);
149 |
150 |         // 重新进入 LLM 处理流程
151 |         const llmMiddlewareIndex = this.services.middlewareManager.findIndex("llm-processing");
152 |         await this.services.middlewareManager.executeFrom(ctx, llmMiddlewareIndex);
153 |     }
154 |
155 |     /**
156 |      * 结束处理流程，重置状态并释放频道。
157 |      */
158 |     private async _finalizeProcessing(ctx: MessageContext): Promise<void> {
159 |         await ctx.transitionTo(ConversationState.IDLE);
160 |         ctx.heartbeatCount = 0;
161 |         ctx.koishiContext.emit("channel:processing:release", ctx.koishiSession.channelId);
162 |         this.logger.info("🚦 频道状态已重置为 IDLE，处理流程结束。");
163 |     }
164 |
165 |     /**
166 |      * 执行单个工具调用，包含优化的重试逻辑。
167 |      */
168 |     async executeToolCall(ctx: MessageContext, functionName: string, params: Record<string, unknown>): Promise<ToolCallResult> {
169 |         const toolManager = this.ctx["yesimbot.tool"];
170 |         const tool = toolManager.getTool(functionName);
171 |
172 |         if (!tool) {
173 |             this.logger.warn(`[❌ Failed] 工具 '${functionName}' 未找到。`);
174 |             return Failed(`Tool ${functionName} not found`);
175 |         }
176 |
177 |         const maxRetry = this.config.maxRetry ?? ResponseHandlingMiddleware.DEFAULT_MAX_RETRY;
178 |         let lastResult: ToolCallResult = Failed("Tool call did not execute.");
179 |
180 |         const stringifyParams = Object.entries(params)
181 |             .map(([key, value]) => `${key}=${JSON.stringify(value)}`)
182 |             .join(", ");
183 |
184 |         this.logger.info(`[⚙️ Action] → 调用工具: ${functionName}(${stringifyParams})`);
185 |
186 |         for (let attempt = 1; attempt <= maxRetry + 1; attempt++) {
187 |             try {
188 |                 // 仅在重试时（非首次尝试）输出日志并等待
189 |                 if (attempt > 1) {
190 |                     this.logger.info(`  - 第 ${attempt - 1}/${maxRetry} 次重试...`);
191 |                     await new Promise((resolve) => setTimeout(resolve, ResponseHandlingMiddleware.RETRY_DELAY_MS));
192 |                 }
193 |
194 |                 lastResult = await tool.execute(params, {
195 |                     koishiContext: ctx.koishiContext,
196 |                     koishiSession: ctx.koishiSession,
197 |                     platform: ctx.platform,
198 |                 });
199 |
200 |                 if (lastResult.success) {
201 |                     this.logger.info(`[✔️ Success] ← 工具返回: ${JSON.stringify(lastResult)}`);
202 |                     return lastResult;
203 |                 }
204 |
205 |                 // 如果失败了，检查是否允许重试
206 |                 if (!lastResult.retryable) {
207 |                     this.logger.warn(`[❌ Failed] ← 工具执行失败且不可重试: ${lastResult.error}`);
208 |                     return lastResult;
209 |                 }
210 |
211 |                 this.logger.warn(`[⚠️ Retryable] ← 工具执行失败，准备重试。原因: ${lastResult.error}`);
212 |             } catch (error) {
213 |                 this.logger.error(`[❌ Error] 工具 '${functionName}' 执行时抛出异常: %s`, error.message);
214 |                 this.logger.error(error.stack);
215 |                 lastResult = Failed(`Exception during tool execution: ${error.message}`);
216 |                 // 发生异常通常不可重试
217 |                 return lastResult;
218 |             }
219 |         }
220 |
221 |         this.logger.error(`[❌ Failed] ← 工具 '${functionName}' 在 ${maxRetry} 次重试后仍然失败。`);
222 |         return lastResult;
223 |     }
224 |
225 |     private async recordToolCall(ctx: MessageContext, functionName: string, params: Record<string, unknown>): Promise<void> {
226 |         const newInteraction: Interaction = {
227 |             id: Random.id(),
228 |             emitter: ctx.koishiSession.messageId,
229 |             emitter_channel_id: ctx.koishiSession.channelId,
230 |             type: "tool_call",
231 |             functionName,
232 |             toolParams: params,
233 |             life: this.config.life ?? ResponseHandlingMiddleware.DEFAULT_LIFE,
234 |             timestamp: new Date(),
235 |         };
236 |         await this.ctx.database.create(INTERACTION_TABLE, newInteraction);
237 |         await this.services.scenarioManager.updateInteraction(newInteraction, ctx.koishiSession, false);
238 |     }
239 |
240 |     private async recordToolResult(ctx: MessageContext, functionName: string, result: ToolCallResult): Promise<void> {
241 |         if (functionName === "send_message") return;
242 |
243 |         const newInteraction: Interaction = {
244 |             id: Random.id(),
245 |             emitter: ctx.koishiSession.messageId,
246 |             emitter_channel_id: ctx.koishiSession.channelId,
247 |             type: "tool_result",
248 |             functionName,
249 |             toolResult: result,
250 |             life: this.config.life ?? ResponseHandlingMiddleware.DEFAULT_LIFE,
251 |             timestamp: new Date(),
252 |         };
253 |         await this.ctx.database.create(INTERACTION_TABLE, newInteraction);
254 |         await this.services.scenarioManager.updateInteraction(newInteraction, ctx.koishiSession, true);
255 |     }
256 |
257 |     /**
258 |      * 从字符串中提取 JSON 对象。
259 |      * (保持原实现，因为它处理了多种 JSON 格式)
260 |      */
261 |     private _extractJson(text: string): any[] {
262 |         const results = [];
263 |         const jsonRegex = /```json\s*([\s\S]*?)```|(\{[\s\S]*\}|\[[\s\S]*\])/g;
264 |
265 |         let match;
266 |         while ((match = jsonRegex.exec(text)) !== null) {
267 |             const jsonString = match[1] ? match[1].trim() : match[2]?.trim();
268 |             if (!jsonString) continue;
269 |
270 |             try {
271 |                 const parsedJson = JSON.parse(jsonString);
272 |                 results.push(...(Array.isArray(parsedJson) ? parsedJson : [parsedJson]));
273 |             } catch (e) {
274 |                 try {
275 |                     const parsedJson = extractJSONFromString(jsonString, "object");
276 |                     results.push(...parsedJson);
277 |                 } catch (error) {
278 |                     this.logger.debug("无效的 JSON 候选被忽略: %s", jsonString);
279 |                 }
280 |             }
281 |         }
282 |         return results;
283 |     }
284 | }

</file_content>
</file_content>

<file_content path="external/YesImBot/packages/core/src/services">
├── ConversationFlowAnalyzer.ts
├── DatabaseManager.ts
├── MiddlewareConfigurator.ts
├── PlatformAdapter.ts
├── ServiceContainer.ts
├── ServiceInitializer.ts
└── worldstate/

<file_content path="external/YesImBot/packages/core/src/services/ConversationFlowAnalyzer.ts">
  1 | import { Context } from "koishi";
  2 | import { ChatMessage } from "../types/model";
  3 |
  4 | // 消息关联类型
  5 | export type MessageRelationType =
  6 |     | "topic_continuation" // 话题延续
  7 |     | "topic_shift" // 话题转移
  8 |     | "response_to_previous" // 回应之前的消息
  9 |     | "new_topic" // 全新话题
 10 |     | "side_conversation"; // 旁支对话
 11 |
 12 | // 话题状态
 13 | export type TopicStatus =
 14 |     | "developing" // 正在发展
 15 |     | "stable" // 稳定讨论
 16 |     | "cooling" // 逐渐冷却
 17 |     | "ended"; // 已结束
 18 |
 19 | // 消息分析结果
 20 | export interface MessageAnalysis {
 21 |     messageId: string;
 22 |     relationType: MessageRelationType;
 23 |     topicId?: string;
 24 |     referencedMessageIds: string[];
 25 |     confidence: number; // 0-1，分析的置信度
 26 |     timestamp: Date;
 27 | }
 28 |
 29 | // 话题分析结果
 30 | export interface TopicAnalysis {
 31 |     topicId: string;
 32 |     status: TopicStatus;
 33 |     participants: Set<string>;
 34 |     lastActivity: Date;
 35 |     messageCount: number;
 36 |     keywords: string[];
 37 |     stability: number; // 话题稳定性 0-1
 38 | }
 39 |
 40 | // 对话流状态
 41 | export interface ConversationFlow {
 42 |     activeTopics: Map<string, TopicAnalysis>;
 43 |     recentMessages: MessageAnalysis[];
 44 |     conversationPace: "fast" | "normal" | "slow";
 45 |     lastAnalysisTime: Date;
 46 | }
 47 |
 48 | // 回复决策结果
 49 | export interface ReplyDecision {
 50 |     shouldReply: boolean;
 51 |     reason: string;
 52 |     confidence: number;
 53 |     suggestedWaitTime?: number;
 54 | }
 55 |
 56 | export class ConversationFlowAnalyzer {
 57 |     private flows = new Map<string, ConversationFlow>();
 58 |     private readonly maxRecentMessages = 15;
 59 |     private readonly topicTimeoutMs = 10 * 60 * 1000; // 10分钟话题超时
 60 |
 61 |     constructor(private ctx: Context) {}
 62 |
 63 |     /**
 64 |      * 分析新消息并更新对话流
 65 |      */
 66 |     public async analyzeMessage(channelId: string, message: ChatMessage): Promise<MessageAnalysis> {
 67 |         let flow = this.flows.get(channelId);
 68 |         if (!flow) {
 69 |             flow = {
 70 |                 activeTopics: new Map(),
 71 |                 recentMessages: [],
 72 |                 conversationPace: "normal",
 73 |                 lastAnalysisTime: new Date(),
 74 |             };
 75 |             this.flows.set(channelId, flow);
 76 |         }
 77 |
 78 |         // 分析消息关联性
 79 |         const analysis = await this.analyzeMessageRelation(message, flow);
 80 |
 81 |         // 更新对话流状态
 82 |         this.updateConversationFlow(flow, analysis, message);
 83 |
 84 |         // 清理过期话题
 85 |         this.cleanupExpiredTopics(flow);
 86 |
 87 |         return analysis;
 88 |     }
 89 |
 90 |     /**
 91 |      * 判断是否适合回复
 92 |      */
 93 |     public shouldReply(channelId: string, currentMessage: ChatMessage): ReplyDecision {
 94 |         const flow = this.flows.get(channelId);
 95 |         if (!flow) {
 96 |             return { shouldReply: false, reason: "no_flow_data", confidence: 0 };
 97 |         }
 98 |
 99 |         // 如果被@，立即回复
100 |         if (this.isDirectMention(currentMessage)) {
101 |             return {
102 |                 shouldReply: true,
103 |                 reason: "direct_mention",
104 |                 confidence: 1.0,
105 |                 suggestedWaitTime: 1000, // 1秒快速响应
106 |             };
107 |         }
108 |
109 |         // 分析话题状态
110 |         const topicAnalysis = this.analyzeTopicReadiness(flow);
111 |
112 |         // 分析对话节奏
113 |         const paceAnalysis = this.analyzePaceReadiness(flow);
114 |
115 |         // 综合判断
116 |         const confidence = (topicAnalysis.confidence + paceAnalysis.confidence) / 2;
117 |         const shouldReply = confidence > 0.6;
118 |
119 |         // 计算建议等待时间
120 |         const suggestedWaitTime = this.calculateSuggestedWaitTime(flow, topicAnalysis, paceAnalysis);
121 |
122 |         return {
123 |             shouldReply,
124 |             reason: shouldReply ? topicAnalysis.reason : "topic_still_developing",
125 |             confidence,
126 |             suggestedWaitTime,
127 |         };
128 |     }
129 |
130 |     /**
131 |      * 分析消息关联性
132 |      */
133 |     private async analyzeMessageRelation(message: ChatMessage, flow: ConversationFlow): Promise<MessageAnalysis> {
134 |         const recentMessages = flow.recentMessages.slice(-8); // 分析最近8条消息
135 |
136 |         // 提取关键词
137 |         const keywords = this.extractKeywords(message.content as string);
138 |         let bestMatch: { topicId?: string; confidence: number; type: MessageRelationType } = {
139 |             confidence: 0,
140 |             type: "new_topic",
141 |         };
142 |
143 |         // 检查是否与现有话题相关
144 |         for (const [topicId, topic] of flow.activeTopics) {
145 |             const similarity = this.calculateTopicSimilarity(keywords, topic.keywords);
146 |             if (similarity > bestMatch.confidence) {
147 |                 bestMatch = {
148 |                     topicId,
149 |                     confidence: similarity,
150 |                     type: similarity > 0.7 ? "topic_continuation" : "topic_shift",
151 |                 };
152 |             }
153 |         }
154 |
155 |         // 检查是否回应之前的消息
156 |         const referencedMessages = this.findReferencedMessages(message, recentMessages);
157 |         if (referencedMessages.length > 0 && bestMatch.confidence < 0.8) {
158 |             bestMatch = {
159 |                 confidence: 0.8,
160 |                 type: "response_to_previous",
161 |             };
162 |         }
163 |
164 |         return {
165 |             messageId: message.messageId,
166 |             relationType: bestMatch.type,
167 |             topicId: bestMatch.topicId,
168 |             referencedMessageIds: referencedMessages,
169 |             confidence: bestMatch.confidence,
170 |             timestamp: message.timestamp,
171 |         };
172 |     }
173 |
174 |     /**
175 |      * 更新对话流状态
176 |      */
177 |     private updateConversationFlow(flow: ConversationFlow, analysis: MessageAnalysis, message: ChatMessage): void {
178 |         // 添加到最近消息
179 |         flow.recentMessages.push(analysis);
180 |         if (flow.recentMessages.length > this.maxRecentMessages) {
181 |             flow.recentMessages.shift();
182 |         }
183 |
184 |         // 更新话题状态
185 |         if (analysis.topicId) {
186 |             const topic = flow.activeTopics.get(analysis.topicId);
187 |             if (topic) {
188 |                 topic.lastActivity = analysis.timestamp;
189 |                 topic.messageCount++;
190 |                 topic.participants.add(message.sender.id);
191 |                 topic.status = this.determineTopicStatus(topic, flow.recentMessages);
192 |
193 |                 // 更新关键词
194 |                 const newKeywords = this.extractKeywords(message.content as string);
195 |                 topic.keywords = [...new Set([...topic.keywords, ...newKeywords])].slice(0, 10);
196 |             }
197 |         } else if (analysis.relationType === "new_topic") {
198 |             // 创建新话题
199 |             const newTopicId = `topic_${Date.now()}_${message.sender.id}`;
200 |             const keywords = this.extractKeywords(message.content as string);
201 |
202 |             flow.activeTopics.set(newTopicId, {
203 |                 topicId: newTopicId,
204 |                 status: "developing",
205 |                 participants: new Set([message.sender.id]),
206 |                 lastActivity: analysis.timestamp,
207 |                 messageCount: 1,
208 |                 keywords: keywords,
209 |                 stability: 0.1,
210 |             });
211 |         }
212 |
213 |         // 更新对话节奏
214 |         flow.conversationPace = this.calculateConversationPace(flow.recentMessages);
215 |         flow.lastAnalysisTime = new Date();
216 |     }
217 |
218 |     /**
219 |      * 分析话题准备状态
220 |      */
221 |     private analyzeTopicReadiness(flow: ConversationFlow): { confidence: number; reason: string } {
222 |         const activeTopics = Array.from(flow.activeTopics.values());
223 |
224 |         // 如果没有活跃话题，可以回复
225 |         if (activeTopics.length === 0) {
226 |             return { confidence: 0.8, reason: "no_active_topics" };
227 |         }
228 |
229 |         // 检查话题是否稳定或冷却
230 |         const stableTopics = activeTopics.filter((t) => t.status === "stable" || t.status === "cooling");
231 |         if (stableTopics.length > 0) {
232 |             return { confidence: 0.7, reason: "topics_stable" };
233 |         }
234 |
235 |         // 检查话题是否已经有足够的讨论
236 |         const matureTopics = activeTopics.filter((t) => t.messageCount >= 3);
237 |         if (matureTopics.length > 0) {
238 |             return { confidence: 0.6, reason: "topics_mature" };
239 |         }
240 |
241 |         return { confidence: 0.2, reason: "topics_developing" };
242 |     }
243 |
244 |     /**
245 |      * 分析节奏准备状态
246 |      */
247 |     private analyzePaceReadiness(flow: ConversationFlow): { confidence: number; reason: string } {
248 |         const recentMessages = flow.recentMessages.slice(-5);
249 |         if (recentMessages.length < 2) {
250 |             return { confidence: 0.5, reason: "insufficient_data" };
251 |         }
252 |
253 |         // 计算消息间隔
254 |         const intervals = [];
255 |         for (let i = 1; i < recentMessages.length; i++) {
256 |             const interval = recentMessages[i].timestamp.getTime() - recentMessages[i - 1].timestamp.getTime();
257 |             intervals.push(interval);
258 |         }
259 |
260 |         const avgInterval = intervals.reduce((a, b) => a + b, 0) / intervals.length;
261 |
262 |         // 如果最近的消息间隔较大，说明对话节奏放缓，适合回复
263 |         if (avgInterval > 30000) {
264 |             // 30秒
265 |             return { confidence: 0.8, reason: "conversation_slowing" };
266 |         }
267 |
268 |         // 如果节奏很快，等待一下
269 |         if (avgInterval < 5000) {
270 |             // 5秒
271 |             return { confidence: 0.2, reason: "conversation_too_fast" };
272 |         }
273 |
274 |         return { confidence: 0.5, reason: "normal_pace" };
275 |     }
276 |
277 |     /**
278 |      * 计算建议等待时间
279 |      */
280 |     private calculateSuggestedWaitTime(
281 |         flow: ConversationFlow,
282 |         topicAnalysis: { confidence: number; reason: string },
283 |         paceAnalysis: { confidence: number; reason: string }
284 |     ): number {
285 |         let baseWaitTime = 3000; // 基础3秒
286 |
287 |         // 根据话题状态调整
288 |         switch (topicAnalysis.reason) {
289 |             case "topics_developing":
290 |                 baseWaitTime *= 2.0; // 话题发展中，延长等待
291 |                 break;
292 |             case "topics_stable":
293 |                 baseWaitTime *= 0.8; // 话题稳定，可以适当缩短
294 |                 break;
295 |             case "no_active_topics":
296 |                 baseWaitTime *= 0.6; // 无活跃话题，可以更快回复
297 |                 break;
298 |         }
299 |
300 |         // 根据节奏调整
301 |         switch (paceAnalysis.reason) {
302 |             case "conversation_too_fast":
303 |                 baseWaitTime *= 2.5; // 对话太快，大幅延长
304 |                 break;
305 |             case "conversation_slowing":
306 |                 baseWaitTime *= 0.5; // 对话放缓，可以更快插入
307 |                 break;
308 |         }
309 |
310 |         return Math.max(1000, Math.min(baseWaitTime, 8000)); // 限制在1-8秒
311 |     }
312 |
313 |     /**
314 |      * 提取关键词
315 |      */
316 |     private extractKeywords(content: string): string[] {
317 |         // 简化的关键词提取
318 |         const cleanContent = content
319 |             .toLowerCase()
320 |             .replace(/[^\u4e00-\u9fa5a-zA-Z0-9\s]/g, " ")
321 |             .replace(/\s+/g, " ")
322 |             .trim();
323 |
324 |         const words = cleanContent
325 |             .split(" ")
326 |             .filter((word) => word.length > 1)
327 |             .filter((word) => !this.isStopWord(word));
328 |
329 |         return [...new Set(words)].slice(0, 5);
330 |     }
331 |
332 |     /**
333 |      * 判断是否为停用词
334 |      */
335 |     private isStopWord(word: string): boolean {
336 |         const stopWords = new Set([
337 |             "的",
338 |             "了",
339 |             "是",
340 |             "在",
341 |             "我",
342 |             "你",
343 |             "他",
344 |             "她",
345 |             "它",
346 |             "这",
347 |             "那",
348 |             "有",
349 |             "和",
350 |             "与",
351 |             "就",
352 |             "都",
353 |             "要",
354 |             "会",
355 |             "能",
356 |             "可以",
357 |             "不是",
358 |             "the",
359 |             "a",
360 |             "an",
361 |             "and",
362 |             "or",
363 |             "but",
364 |             "in",
365 |             "on",
366 |             "at",
367 |             "to",
368 |             "for",
369 |             "of",
370 |             "with",
371 |             "by",
372 |             "is",
373 |             "are",
374 |             "was",
375 |             "were",
376 |             "be",
377 |         ]);
378 |         return stopWords.has(word);
379 |     }
380 |
381 |     /**
382 |      * 计算话题相似度
383 |      */
384 |     private calculateTopicSimilarity(keywords1: string[], keywords2: string[]): number {
385 |         if (keywords1.length === 0 || keywords2.length === 0) return 0;
386 |
387 |         const set1 = new Set(keywords1);
388 |         const set2 = new Set(keywords2);
389 |         const intersection = new Set([...set1].filter((k) => set2.has(k)));
390 |         const union = new Set([...set1, ...set2]);
391 |
392 |         return intersection.size / union.size;
393 |     }
394 |
395 |     /**
396 |      * 查找引用的消息
397 |      */
398 |     private findReferencedMessages(message: ChatMessage, recentMessages: MessageAnalysis[]): string[] {
399 |         const content = message.content as string;
400 |         const replyKeywords = ["回复", "回应", "@", "刚才", "上面", "之前", "刚刚"];
401 |
402 |         if (replyKeywords.some((keyword) => content.includes(keyword))) {
403 |             return recentMessages.slice(-3).map((m) => m.messageId);
404 |         }
405 |
406 |         return [];
407 |     }
408 |
409 |     /**
410 |      * 确定话题状态
411 |      */
412 |     private determineTopicStatus(topic: TopicAnalysis, recentMessages: MessageAnalysis[]): TopicStatus {
413 |         const now = Date.now();
414 |         const timeSinceLastActivity = now - topic.lastActivity.getTime();
415 |
416 |         // 超过10分钟，话题结束
417 |         if (timeSinceLastActivity > 10 * 60 * 1000) {
418 |             return "ended";
419 |         }
420 |
421 |         // 超过5分钟没有相关消息，话题冷却
422 |         if (timeSinceLastActivity > 5 * 60 * 1000) {
423 |             return "cooling";
424 |         }
425 |
426 |         // 根据消息数量和参与者判断
427 |         if (topic.messageCount >= 5 && topic.participants.size >= 2) {
428 |             return "stable";
429 |         }
430 |
431 |         return "developing";
432 |     }
433 |
434 |     /**
435 |      * 计算对话节奏
436 |      */
437 |     private calculateConversationPace(recentMessages: MessageAnalysis[]): "fast" | "normal" | "slow" {
438 |         if (recentMessages.length < 3) return "normal";
439 |
440 |         const intervals = [];
441 |         for (let i = 1; i < Math.min(recentMessages.length, 6); i++) {
442 |             const interval = recentMessages[i].timestamp.getTime() - recentMessages[i - 1].timestamp.getTime();
443 |             intervals.push(interval);
444 |         }
445 |
446 |         const avgInterval = intervals.reduce((a, b) => a + b, 0) / intervals.length;
447 |
448 |         if (avgInterval < 10000) return "fast"; // 10秒内
449 |         if (avgInterval > 60000) return "slow"; // 1分钟以上
450 |         return "normal";
451 |     }
452 |
453 |     /**
454 |      * 检查是否直接提及
455 |      */
456 |     private isDirectMention(message: ChatMessage): boolean {
457 |         const content = message.content as string;
458 |         // 简化的@检测，实际应该检查具体的@目标
459 |         return (
460 |             content.includes("@") ||
461 |             content.includes("机器人") ||
462 |             content.includes("bot") ||
463 |             content.includes("AI") ||
464 |             content.includes("助手")
465 |         );
466 |     }
467 |
468 |     /**
469 |      * 清理过期话题
470 |      */
471 |     private cleanupExpiredTopics(flow: ConversationFlow): void {
472 |         const now = Date.now();
473 |         for (const [topicId, topic] of flow.activeTopics) {
474 |             if (now - topic.lastActivity.getTime() > this.topicTimeoutMs) {
475 |                 flow.activeTopics.delete(topicId);
476 |             }
477 |         }
478 |     }
479 |
480 |     /**
481 |      * 获取对话流状态
482 |      */
483 |     public getConversationFlow(channelId: string): ConversationFlow | null {
484 |         return this.flows.get(channelId) || null;
485 |     }
486 |
487 |     /**
488 |      * 获取调试信息
489 |      */
490 |     public getDebugInfo(channelId: string): any {
491 |         const flow = this.flows.get(channelId);
492 |         if (!flow) return null;
493 |
494 |         return {
495 |             activeTopicsCount: flow.activeTopics.size,
496 |             recentMessagesCount: flow.recentMessages.length,
497 |             conversationPace: flow.conversationPace,
498 |             topics: Array.from(flow.activeTopics.values()).map((topic) => ({
499 |                 id: topic.topicId,
500 |                 status: topic.status,
501 |                 messageCount: topic.messageCount,
502 |                 participantsCount: topic.participants.size,
503 |                 keywords: topic.keywords,
504 |             })),
505 |         };
506 |     }
507 | }

</file_content>

<file_content path="external/YesImBot/packages/core/src/services/DatabaseManager.ts">
 1 | import { Context } from "koishi";
 2 | import { IMAGE_TABLE, INTERACTION_TABLE, LAST_REPLY_TABLE, MESSAGE_TABLE } from "../types/model";
 3 |
 4 | /**
 5 |  * 数据库管理器
 6 |  * 负责所有数据库表的注册和初始化
 7 |  */
 8 | export class DatabaseManager {
 9 |     constructor(private ctx: Context) {}
10 |
11 |     /**
12 |      * 注册所有数据库表
13 |      */
14 |     public registerTables(): void {
15 |         this.registerMessageTable();
16 |         this.registerInteractionTable();
17 |         this.registerLastReplyTable();
18 |         this.registerImageTable();
19 |
20 |         this.ctx.logger.info("[DatabaseManager] 所有数据库表注册完成");
21 |     }
22 |
23 |     private registerMessageTable(): void {
24 |         this.ctx.model.extend(
25 |             MESSAGE_TABLE,
26 |             {
27 |                 messageId: "string",
28 |                 sender: "object",
29 |                 channel: "object",
30 |                 timestamp: "timestamp",
31 |                 content: "string",
32 |             },
33 |             {
34 |                 primary: ["messageId"],
35 |                 autoInc: false,
36 |             }
37 |         );
38 |     }
39 |
40 |     private registerInteractionTable(): void {
41 |         this.ctx.model.extend(
42 |             INTERACTION_TABLE,
43 |             {
44 |                 id: "string",
45 |                 emitter: "string",
46 |                 emitter_channel_id: "string",
47 |                 type: "string",
48 |                 functionName: "string",
49 |                 toolParams: "json",
50 |                 toolResult: "object",
51 |                 life: "integer",
52 |                 timestamp: "timestamp",
53 |             },
54 |             {
55 |                 primary: "id",
56 |             }
57 |         );
58 |     }
59 |
60 |     private registerLastReplyTable(): void {
61 |         this.ctx.model.extend(
62 |             LAST_REPLY_TABLE,
63 |             {
64 |                 channelId: "string",
65 |                 timestamp: "timestamp",
66 |             },
67 |             {
68 |                 primary: "channelId",
69 |                 autoInc: false,
70 |             }
71 |         );
72 |     }
73 |
74 |     private registerImageTable(): void {
75 |         this.ctx.model.extend(
76 |             IMAGE_TABLE,
77 |             {
78 |                 id: "string",
79 |                 mimeType: "string",
80 |                 base64: "string",
81 |                 summary: "string",
82 |                 desc: "string",
83 |                 size: "integer",
84 |                 timestamp: "timestamp",
85 |             },
86 |             {
87 |                 primary: "id",
88 |                 autoInc: false,
89 |             }
90 |         );
91 |     }
92 | }

</file_content>

<file_content path="external/YesImBot/packages/core/src/services/MiddlewareConfigurator.ts">
  1 | import { Context } from "koishi";
  2 | import { ChatModelSwitcher } from "../adapters";
  3 | import { Config } from "../config";
  4 | import { MiddlewareManager } from "../middleware/base";
  5 | import { CheckReplyCondition } from "../middleware/CheckReplyCondition";
  6 | import { DatabaseStorageMiddleware } from "../middleware/DatabaseStorage";
  7 | import { ErrorHandlingMiddleware } from "../middleware/ErrorHandling";
  8 | import { LLMProcessingMiddleware } from "../middleware/LLMProcessing";
  9 | import { ResponseHandlingMiddleware } from "../middleware/ResponseHandling";
 10 | import { PromptBuilder } from "../prompt/PromptBuilder";
 11 | import { ImageProcessor } from "../utils";
 12 | import { ScenarioManager } from "./scenario/ScenarioManager";
 13 | import { IServiceContainer, SERVICE_TOKENS } from "./ServiceContainer";
 14 |
 15 | /**
 16 |  * 中间件配置器
 17 |  * 负责配置和组装中间件链
 18 |  */
 19 | export class MiddlewareConfigurator {
 20 |     constructor(private ctx: Context, private config: Config, private container: IServiceContainer) {}
 21 |
 22 |     /**
 23 |      * 配置中间件链
 24 |      */
 25 |     public configure(): MiddlewareManager {
 26 |         const middlewareManager = this.container.get<MiddlewareManager>(SERVICE_TOKENS.MIDDLEWARE_MANAGER);
 27 |
 28 |         this.setupMiddlewareChain(middlewareManager);
 29 |         this.registerCleanupHandlers();
 30 |
 31 |         return middlewareManager;
 32 |     }
 33 |
 34 |     private setupMiddlewareChain(middlewareManager: MiddlewareManager): void {
 35 |         // 错误处理中间件
 36 |         middlewareManager.use(
 37 |             new ErrorHandlingMiddleware(this.ctx, {
 38 |                 debug: this.config.Debug.EnableDebug,
 39 |                 uploadDump: this.config.Debug.UploadDump,
 40 |                 pasteServiceUrl: "https://dump.yesimbot.chat/",
 41 |                 includeFullSessionContent: false,
 42 |             })
 43 |         );
 44 |
 45 |         // 数据库存储中间件
 46 |         const imageProcessor = this.container.get<ImageProcessor>(SERVICE_TOKENS.IMAGE_PROCESSOR);
 47 |         const scenarioManager = this.container.get<ScenarioManager>(SERVICE_TOKENS.SCENARIO_MANAGER);
 48 |
 49 |         middlewareManager.use(
 50 |             new DatabaseStorageMiddleware(this.ctx, {
 51 |                 imageProcessor,
 52 |                 scenarioManager,
 53 |             })
 54 |         );
 55 |
 56 |         // 检查回复条件中间件（重构版本）
 57 |         const checkReplyMiddleware = new CheckReplyCondition(this.ctx, this.config.ReplyCondition);
 58 |         middlewareManager.use(checkReplyMiddleware);
 59 |
 60 |         // LLM处理中间件
 61 |         const chatModelSwitcher = this.container.get<ChatModelSwitcher>(SERVICE_TOKENS.CHAT_MODEL_SWITCHER);
 62 |         const promptBuilder = this.container.get<PromptBuilder>(SERVICE_TOKENS.PROMPT_BUILDER);
 63 |
 64 |         middlewareManager.use(
 65 |             new LLMProcessingMiddleware(
 66 |                 this.ctx,
 67 |                 {
 68 |                     chatModelSwitcher,
 69 |                     promptBuilder,
 70 |                     scenarioManager,
 71 |                 },
 72 |                 {
 73 |                     debug: this.config.Debug.EnableDebug,
 74 |                     retryConfig: {
 75 |                         maxRetries: this.config.LLM.RetryConfig.MaxRetries,
 76 |                         timeoutMs: this.config.LLM.RetryConfig.TimeoutMs,
 77 |                         retryDelayMs: this.config.LLM.RetryConfig.RetryDelayMs,
 78 |                         exponentialBackoff: this.config.LLM.RetryConfig.ExponentialBackoff,
 79 |                         retryableErrors: this.config.LLM.RetryConfig.RetryableErrors,
 80 |                     },
 81 |                     adapterSwitchingConfig: {
 82 |                         enabled: this.config.LLM.AdapterSwitching.Enabled,
 83 |                         maxAttempts: this.config.LLM.AdapterSwitching.MaxAttempts,
 84 |                     },
 85 |                 }
 86 |             )
 87 |         );
 88 |
 89 |         // 响应处理中间件
 90 |         middlewareManager.use(
 91 |             new ResponseHandlingMiddleware(
 92 |                 this.ctx,
 93 |                 { middlewareManager, scenarioManager },
 94 |                 {
 95 |                     maxRetry: this.config.ToolCall.MaxRetry,
 96 |                     life: this.config.ToolCall.Life,
 97 |                     maxHeartbeat: this.config.Chat.MaxHeartbeat,
 98 |                 }
 99 |             )
100 |         );
101 |     }
102 |
103 |     private registerCleanupHandlers(): void {
104 |         this.ctx.on("dispose", () => {
105 |             const scenarioManager = this.container.get<ScenarioManager>(SERVICE_TOKENS.SCENARIO_MANAGER);
106 |             scenarioManager.clearAllScenarios();
107 |
108 |             const middlewareManager = this.container.get<MiddlewareManager>(SERVICE_TOKENS.MIDDLEWARE_MANAGER);
109 |             const checkReply = middlewareManager.getMiddleware<CheckReplyCondition>("check-reply-condition");
110 |             if (checkReply) {
111 |                 checkReply.destroy();
112 |             }
113 |         });
114 |     }
115 |
116 |     public dispose(): void {
117 |         // 清理逻辑已移到各个中间件内部处理
118 |     }
119 | }

</file_content>

<file_content path="external/YesImBot/packages/core/src/services/PlatformAdapter.ts">
  1 | import { Session, h } from "koishi";
  2 | // import { } from "koishi-plugin-adapter-onebot";
  3 |
  4 | export type GroupRole = 'member' | 'admin' | 'owner';
  5 |
  6 | export interface GroupMemberInfo {
  7 |     area?: string;
  8 |     level?: string;
  9 |     title?: string;
 10 |     role?: GroupRole;
 11 |     card?: string;
 12 |     card_changeable?: boolean;
 13 |     group_id: string;
 14 |     join_time: number;
 15 |     last_sent_time?: number;
 16 |     title_expire_time?: number;
 17 |     unfriendly?: boolean;
 18 |     [key: string]: any;
 19 | }
 20 |
 21 | export interface UserInfo {
 22 |     userId: string;
 23 |     sex?: string;
 24 |     nickname?: string;
 25 |     avatar?: string;
 26 |     sign?: string;
 27 |
 28 |     [key: string]: any;
 29 | }
 30 |
 31 | export interface GroupInfo {
 32 |     groupId: string;
 33 |     name?: string;
 34 |     avatar?: string;
 35 |     maxMemberCount?: number;
 36 |     memberCount?: number;
 37 |     [key: string]: any;
 38 | }
 39 |
 40 | /**
 41 |  * 平台信息适配器接口。
 42 |  * 封装了获取用户和群信息的平台特定逻辑。
 43 |  * @param session 当前会话，可用于获取上下文信息
 44 |  */
 45 | export abstract class PlatformAdapter {
 46 |
 47 |     get name() { return this.session.platform }
 48 |
 49 |     constructor(protected session: Session) { }
 50 |     /**
 51 |      * 获取群信息。
 52 |      * @param groupId 群ID
 53 |      */
 54 |     abstract getGroupInfo(groupId: string): Promise<GroupInfo>;
 55 |
 56 |     /**
 57 |      * 获取用户信息。
 58 |      * @param userId 用户ID
 59 |      */
 60 |     abstract getUserInfo(userId: string): Promise<UserInfo>;
 61 |
 62 |     abstract getGroupMemberInfo(userId: string, groupId: string): Promise<GroupMemberInfo>;
 63 |
 64 |     // 执行指令
 65 |     abstract executeCommand(command: string, channelId: string): Promise<void>;
 66 |
 67 |     // 添加表态
 68 |     abstract createReaction(messageId: string, emojiId: number): Promise<any>;
 69 |
 70 |     // 设置精华消息
 71 |     abstract setEssenceMessage(messageId: string): Promise<any>;
 72 |
 73 |     // 发送戳一戳
 74 |     abstract sendPoke(userId: string, channelId: string): Promise<void>;
 75 |
 76 |     // 获取转发消息
 77 |     abstract getForwardMessage(id: string): Promise<any>;
 78 |
 79 |     // 撤回消息
 80 |     abstract deleteMessage(messageId: string, channelId: string): Promise<void>;
 81 |
 82 |     // 禁言用户
 83 |     abstract muteMember(userId: string, channelId: string, durationMinutes: number): Promise<void>;
 84 | }
 85 |
 86 | export class DefaultPlatform extends PlatformAdapter {
 87 |     constructor(session: Session) {
 88 |         super(session);
 89 |     }
 90 |
 91 |     async getGroupInfo(groupId: string): Promise<GroupInfo> {
 92 |         const groupInfo = await this.session.bot.getGuild(groupId);
 93 |         return {
 94 |             groupId: groupInfo.id,
 95 |             name: groupInfo.name,
 96 |             avatar: groupInfo.avatar,
 97 |         }
 98 |     }
 99 |
100 |     async getUserInfo(userId: string): Promise<UserInfo> {
101 |         const userInfo = await this.session.bot.getUser(userId);
102 |         return {
103 |             userId: userInfo.id,
104 |             nickname: userInfo.nick,
105 |             avatar: userInfo.avatar,
106 |         }
107 |     }
108 |
109 |     async getGroupMemberInfo(userId: string, groupId: string): Promise<GroupMemberInfo> {
110 |         //@ts-ignore
111 |         const memberInfo = await this.session.bot.getGuildMember(groupId, userId);
112 |
113 |         return {
114 |             group_id: groupId,
115 |             //is_robot: memberInfo.is_robot,
116 |             join_time: memberInfo.joinedAt,
117 |             user_id: userId
118 |         }
119 |     }
120 |
121 |     async executeCommand(command: string, channelId: string): Promise<void> {
122 |         await this.session.execute(command);
123 |     }
124 |
125 |     async createReaction(messageId: string, emojiId: number): Promise<any> {
126 |         throw new Error("Not implemented in DefaultPlatform");
127 |     }
128 |
129 |     async setEssenceMessage(messageId: string): Promise<any> {
130 |         throw new Error("Not implemented in DefaultPlatform");
131 |     }
132 |
133 |     async sendPoke(userId: string, channelId: string): Promise<void> {
134 |         throw new Error("Not implemented in DefaultPlatform");
135 |     }
136 |
137 |     async getForwardMessage(id: string): Promise<any> {
138 |         throw new Error("Not implemented in DefaultPlatform");
139 |     }
140 |
141 |     async deleteMessage(messageId: string, channelId: string): Promise<void> {
142 |         await this.session.bot.deleteMessage(channelId, messageId);
143 |     }
144 |
145 |     async muteMember(userId: string, channelId: string, durationMinutes: number): Promise<void> {
146 |         const durationMs = durationMinutes * 60 * 1000;
147 |         await this.session.bot.muteGuildMember(channelId, userId, durationMs);
148 |     }
149 | }
150 |
151 | export class OneBotPlatform extends PlatformAdapter {
152 |     constructor(session: Session) {
153 |         super(session);
154 |     }
155 |
156 |     async getGroupInfo(groupId: string): Promise<GroupInfo> {
157 |         //@ts-ignore
158 |         const groupInfo = await this.session.onebot.getGroupInfo(parseInt(groupId));
159 |         //@ts-ignore
160 |         //const groupNotice = await this.session.onebot.getGroupNotice(groupId);
161 |         //@ts-ignore
162 |         //const groupMemberList = await this.session.onebot.getGroupMemberList(groupId);
163 |
164 |         return {
165 |             groupId: String(groupInfo.group_id),
166 |             name: groupInfo.group_name,
167 |             maxMemberCount: groupInfo.max_member_count,
168 |             memberCount: groupInfo.member_count,
169 |         }
170 |     }
171 |
172 |     async getUserInfo(userId: string): Promise<UserInfo> {
173 |         //@ts-ignore
174 |         const userInfo = await this.session.onebot.getStrangerInfo(parseInt(userId, 10));
175 |
176 |         return {
177 |             userId: String(userInfo.user_id),
178 |             sex: userInfo.sex,
179 |             nickname: userInfo.nickname,
180 |             age: userInfo.age,
181 |         }
182 |     }
183 |
184 |     async getGroupMemberInfo(userId: string, groupId: string): Promise<GroupMemberInfo> {
185 |         //@ts-ignore
186 |         const memberInfo = await this.session.onebot.getGroupMemberInfo(groupId, userId, false);
187 |         /*const memberInfo = await this.session.onebot.getGroupMemberInfo(
188 |             parseInt(groupId, 10),
189 |             parseInt(userId, 10)
190 |         );
191 |         */
192 |
193 |         return {
194 |             //age: memberInfo.age,
195 |             area: memberInfo.area,
196 |             card: memberInfo.card,
197 |             card_changeable: memberInfo.card_changeable,
198 |             group_id: String(memberInfo.group_id),
199 |             //is_robot: memberInfo.is_robot,
200 |             join_time: memberInfo.join_time,
201 |             last_sent_time: memberInfo.last_sent_time,
202 |             level: memberInfo.level,
203 |             //nickname: memberInfo.nickname,
204 |             //qage: memberInfo.qage,
205 |             //qq_level: memberInfo.qq_level,
206 |             role: memberInfo.role,
207 |             sex: memberInfo.sex,
208 |             //shut_up_timestamp: memberInfo.shut_up_timestamp,
209 |             title: memberInfo.title,
210 |             title_expire_time: memberInfo.title_expire_time,
211 |             unfriendly: memberInfo.unfriendly,
212 |             user_id: String(memberInfo.user_id)
213 |         };
214 |     }
215 |
216 |     async executeCommand(command: string, channelId: string): Promise<void> {
217 |         if (channelId === this.session.channelId) {
218 |             await this.session.execute(command);
219 |         } else {
220 |             await this.session.bot.sendMessage(channelId, h("execute", {}, command));
221 |         }
222 |     }
223 |
224 |     async createReaction(messageId: string, emojiId: number): Promise<any> {
225 |         //@ts-ignore
226 |         return this.session.onebot._request("set_msg_emoji_like", {
227 |             message_id: messageId,
228 |             emoji_id: emojiId,
229 |         });
230 |     }
231 |
232 |     async setEssenceMessage(messageId: string): Promise<any> {
233 |         //@ts-ignore
234 |         return this.session.onebot._request("set_essence_msg", { message_id: messageId });
235 |     }
236 |
237 |     async sendPoke(userId: string, channelId: string): Promise<void> {
238 |         if (!channelId.startsWith("private:")) {
239 |         	//@ts-ignore
240 |             await this.session.onebot._request("send_poke", {
241 |                 channel: channelId,
242 |                 group_id: channelId,
243 |                 user_id: userId,
244 |             });
245 |         } else {
246 |         	//@ts-ignore
247 |             await this.session.onebot._request("send_poke", {
248 |                 user_id: userId,
249 |             });
250 |         }
251 |     }
252 |
253 |     async getForwardMessage(id: string): Promise<any> {
254 |     	//@ts-ignore
255 |         return this.session.onebot._request("get_forward_msg", { id });
256 |     }
257 |
258 |     async deleteMessage(messageId: string, channelId: string): Promise<void> {
259 |     	//@ts-ignore
260 |         await this.session.bot.deleteMessage(channelId, messageId);
261 |     }
262 |
263 |     async muteMember(userId: string, channelId: string, durationMinutes: number): Promise<void> {
264 |         const durationMs = durationMinutes * 60 * 1000;
265 |         //@ts-ignore
266 |         await this.session.bot.muteGuildMember(channelId, userId, durationMs);
267 |     }
268 | }

</file_content>

<file_content path="external/YesImBot/packages/core/src/services/ServiceContainer.ts">
 1 | /**
 2 |  * 服务标识符
 3 |  */
 4 | export const SERVICE_TOKENS = {
 5 |     CHAT_MODEL_SWITCHER: Symbol("ChatModelSwitcher"),
 6 |     IMAGE_PROCESSOR: Symbol("ImageProcessor"),
 7 |     SCENARIO_MANAGER: Symbol("ScenarioManager"),
 8 |     PROMPT_BUILDER: Symbol("PromptBuilder"),
 9 |     MIDDLEWARE_MANAGER: Symbol("MiddlewareManager"),
10 |     TOOL_MANAGER: Symbol("ToolManager"),
11 |     MEMORY_SERVICE: Symbol("MemoryService"),
12 |     MODEL_SERVICE: Symbol("ModelService"),
13 | } as const;
14 |
15 | export type ServiceToken = (typeof SERVICE_TOKENS)[keyof typeof SERVICE_TOKENS];
16 |
17 | /**
18 |  * 服务容器接口
19 |  */
20 | export interface IServiceContainer {
21 |     register<T>(token: ServiceToken, factory: () => T): void;
22 |     get<T>(token: ServiceToken): T;
23 |     has(token: ServiceToken): boolean;
24 | }
25 |
26 | /**
27 |  * 服务容器实现
28 |  */
29 | export class ServiceContainer implements IServiceContainer {
30 |     private services = new Map<ServiceToken, any>();
31 |     private factories = new Map<ServiceToken, () => any>();
32 |
33 |     register<T>(token: ServiceToken, factory: () => T): void {
34 |         this.factories.set(token, factory);
35 |     }
36 |
37 |     get<T>(token: ServiceToken): T {
38 |         if (!this.services.has(token)) {
39 |             const factory = this.factories.get(token);
40 |             if (!factory) {
41 |                 throw new Error(`服务未注册: ${token.toString()}`);
42 |             }
43 |             this.services.set(token, factory());
44 |         }
45 |         return this.services.get(token);
46 |     }
47 |
48 |     has(token: ServiceToken): boolean {
49 |         return this.factories.has(token);
50 |     }
51 |
52 |     /**
53 |      * 清理所有服务
54 |      */
55 |     dispose(): void {
56 |         this.services.clear();
57 |         this.factories.clear();
58 |     }
59 | }

</file_content>

<file_content path="external/YesImBot/packages/core/src/services/ServiceInitializer.ts">
  1 | import { Context, Schema, sleep } from "koishi";
  2 | import { Config } from "../config";
  3 | import ToolManager, { createTool, Success, withCommonParams } from "../extensions";
  4 | import { MiddlewareManager } from "../middleware/base";
  5 | import { PromptBuilder } from "../prompt/PromptBuilder";
  6 | import { ChatMessage, MESSAGE_TABLE } from "../types/model";
  7 | import { getChannelType, ImageProcessor, isEmpty } from "../utils";
  8 | import { ScenarioManager } from "./scenario/ScenarioManager";
  9 | import { IServiceContainer, SERVICE_TOKENS } from "./ServiceContainer";
 10 |
 11 | /**
 12 |  * 服务初始化器
 13 |  * 负责初始化和注册所有核心服务
 14 |  */
 15 | export class ServiceInitializer {
 16 |     constructor(private ctx: Context, private config: Config, private container: IServiceContainer) {}
 17 |
 18 |     /**
 19 |      * 初始化所有服务
 20 |      */
 21 |     public async initialize(): Promise<void> {
 22 |         this.registerCoreServices();
 23 |         this.registerTools();
 24 |
 25 |         this.ctx.logger.info("[ServiceInitializer] 所有服务初始化完成");
 26 |     }
 27 |
 28 |     private registerCoreServices(): void {
 29 |         // 注册模型切换器
 30 |         this.container.register(SERVICE_TOKENS.CHAT_MODEL_SWITCHER, () => {
 31 |             return this.ctx["yesimbot.model"].getChatModelSwitcher(this.config.Chat.UseModel);
 32 |         });
 33 |
 34 |         // 注册图片处理器
 35 |         this.container.register(SERVICE_TOKENS.IMAGE_PROCESSOR, () => {
 36 |             return new ImageProcessor(this.ctx);
 37 |         });
 38 |
 39 |         // 注册场景管理器
 40 |         this.container.register(SERVICE_TOKENS.SCENARIO_MANAGER, () => {
 41 |             return new ScenarioManager(this.ctx, this.config.Multimodal, this.config.GroupInfoVisibility, { SlotLimit: 30 });
 42 |         });
 43 |
 44 |         // 注册提示词构建器
 45 |         this.container.register(SERVICE_TOKENS.PROMPT_BUILDER, () => {
 46 |             const scenarioManager = this.container.get<ScenarioManager>(SERVICE_TOKENS.SCENARIO_MANAGER);
 47 |             return new PromptBuilder(this.ctx, scenarioManager, this.config.PromptTemplate, this.config.Multimodal);
 48 |         });
 49 |
 50 |         // 注册中间件管理器
 51 |         this.container.register(SERVICE_TOKENS.MIDDLEWARE_MANAGER, () => {
 52 |             return new MiddlewareManager();
 53 |         });
 54 |
 55 |         // 注册外部服务引用
 56 |         this.container.register(SERVICE_TOKENS.TOOL_MANAGER, () => {
 57 |             return this.ctx["yesimbot.tool"];
 58 |         });
 59 |
 60 |         this.container.register(SERVICE_TOKENS.MEMORY_SERVICE, () => {
 61 |             return this.ctx["yesimbot.memory"];
 62 |         });
 63 |
 64 |         this.container.register(SERVICE_TOKENS.MODEL_SERVICE, () => {
 65 |             return this.ctx["yesimbot.model"];
 66 |         });
 67 |     }
 68 |
 69 | 	private registerTools(): void {
 70 | 		const toolManager: ToolManager = this.container.get(SERVICE_TOKENS.TOOL_MANAGER);
 71 |
 72 | 		// 添加重新加载钩子
 73 | 		toolManager.addReloadHook(async () => {
 74 | 			this.ctx.logger.info("[ServiceInitializer] 重新注册核心工具...");
 75 | 			toolManager.registerTool(this.createSendMessageTool(this.config.Chat));
 76 | 			if (!this.config.Multimodal.Enabled) {
 77 | 			const imageProcessor = this.container.get<ImageProcessor>(SERVICE_TOKENS.IMAGE_PROCESSOR);
 78 | 				toolManager.registerTool(this.createViewImageTool(imageProcessor, this.config.ImageViewer));
 79 | 			}
 80 | 		});
 81 |
 82 | 		// 初始注册
 83 | 		toolManager.registerTool(this.createSendMessageTool(this.config.Chat));
 84 |
 85 | 		if (!this.config.Multimodal.Enabled) {
 86 | 			const imageProcessor = this.container.get<ImageProcessor>(SERVICE_TOKENS.IMAGE_PROCESSOR);
 87 | 			toolManager.registerTool(this.createViewImageTool(imageProcessor, this.config.ImageViewer));
 88 | 		}
 89 | 	}
 90 |
 91 |     private createSendMessageTool(config: Config["Chat"]) {
 92 |         const separator = "<sep/>";
 93 |         return createTool({
 94 |             metadata: {
 95 |                 name: "send_message",
 96 |                 version: "1.0.0",
 97 |                 description: "Sends a message to the human user.",
 98 |             },
 99 |
100 |             parameters: withCommonParams({
101 |                 message: Schema.string().description(
102 |                     `Message content. Use \`${separator}\` to separate sentences. Each segment will be sent individually to mimic human-like typing rhythm. Keep messages short.`
103 |                 ),
104 |                 channel_id: Schema.string().description(
105 |                     "The ID of the channel where the message should be sent. If not provided, the message will default to the current channel."
106 |                 ),
107 |             }),
108 |             execute: async ({ message, channel_id }, context) => {
109 |                 const { koishiContext, koishiSession } = context;
110 |                 const messages = message
111 |                     .split(/<\s*sep\s*\/?\s*>/i)
112 |                     .map((seg) => seg.trim())
113 |                     .filter((seg) => !isEmpty(seg));
114 |
115 |                 let idx = 1;
116 |                 let delay = true;
117 |                 if (!channel_id) {
118 |                     channel_id = koishiSession.channelId;
119 |                 }
120 |
121 |                 for await (const seg of messages) {
122 |                     if (isEmpty(seg)) continue;
123 |                     // 如果是最后一条消息，不延迟
124 |                     if (idx++ >= messages.length) {
125 |                         delay = false;
126 |                     }
127 |                     let messageIds = await koishiSession.sendQueued(seg);
128 |
129 |                     const selfName = koishiSession.bot.user?.name || koishiSession.bot.selfId;
130 |                     const selfNick = koishiSession.bot.user?.nick || selfName;
131 |
132 |                     const newMessage: ChatMessage = {
133 |                         messageId: messageIds[0],
134 |                         sender: {
135 |                             id: koishiSession.bot.selfId,
136 |                             name: selfName,
137 |                             nick: selfNick,
138 |                         },
139 |                         channel: {
140 |                             id: channel_id,
141 |                             type: getChannelType(channel_id),
142 |                         },
143 |                         timestamp: new Date(),
144 |                         content: seg,
145 |                     };
146 |                     await koishiContext.database.create(MESSAGE_TABLE, newMessage);
147 |                     const scenarioManager: ScenarioManager = this.container.get(SERVICE_TOKENS.SCENARIO_MANAGER);
148 |                     scenarioManager.updateMessage(newMessage, koishiSession, false);
149 |                     koishiContext.logger.info(`Message Sent: ${seg}`);
150 |                     if (delay && config.WordsPerSecond > 0) {
151 |                         await sleep((seg.length / config.WordsPerSecond) * 1000);
152 |                     }
153 |                 }
154 |                 return Success();
155 |             },
156 |         });
157 |     }
158 |
159 |     private createViewImageTool(imageProcessor: ImageProcessor, config: Config["ImageViewer"]) {
160 |         return createTool({
161 |             metadata: {
162 |                 name: "view_image",
163 |                 description:
164 |                     "获取聊天记录中指定图片内容的详细描述。当对话需要你理解图片内容才能做出响应时调用此工具。请在需要查看图片以回答用户问题、识别图片中的信息、或理解图片传达的场景时使用。",
165 |             },
166 |
167 |             parameters: withCommonParams({
168 |                 image_id: Schema.string().description("聊天记录中图片的唯一ID。"),
169 |                 query: Schema.string().description(
170 |                     "你希望了解图片的具体内容或方面。例如：'描述图片主要内容'，'图片中有哪些文字？'，'图片中人物的表情是什么？'，'分析图片传递的情绪或场景'，'总结图片的关键信息'。请尽可能具体。如果你不指定，将提供图片的主要内容描述。"
171 |                 ),
172 |             }),
173 |             async execute({ image_id, query }, context) {
174 |                 const { koishiContext } = context;
175 |                 const chatModel = koishiContext["yesimbot.model"].getChatModel(config.UseModel);
176 |
177 |                 const prefix = "你是一个图像分析专家。请根据以下指令，详细分析提供的图片。";
178 |                 const suffix = "请直接输出分析结果，无需额外寒暄。避免提及你无法直接看到图片。你的回答应该简洁、信息丰富且直接回应指令。";
179 |
180 |                 const prompt = [];
181 |
182 |                 prompt.push(prefix);
183 |
184 |                 if (query === "描述图片主要内容") {
185 |                     prompt.push("请提供图片主要内容、场景、主要物体和人物的详细描述，力求准确、客观和全面。");
186 |                 } else if (query.includes("文字") || query.includes("文本")) {
187 |                     prompt.push("请识别图片中所有可读的文字，并列出它们。如果文字内容较长，请进行概括或截取关键信息。");
188 |                 } else if (query.includes("人物") || query.includes("脸")) {
189 |                     prompt.push(
190 |                         "请识别图片中的人物（如果有），描述他们的特征、衣着或表情。如果能识别出身份（名人、通用职业如医生），请指出。"
191 |                     );
192 |                 } else if (query.includes("情绪") || query.includes("氛围")) {
193 |                     prompt.push("请分析图片所传达的情绪、氛围或人物的表情，并解释你的判断依据。");
194 |                 } else if (query.includes("异常") || query.includes("特殊")) {
195 |                     prompt.push("请仔细观察图片，找出其中可能存在的异常、特殊或不寻常之处，并进行描述。");
196 |                 } else if (query.includes("总结") || query.includes("关键信息")) {
197 |                     prompt.push("请总结图片的核心内容和主要信息，突出重点。");
198 |                 } else {
199 |                     prompt.push(`请严格根据以下要求分析图片并提供信息：'${query}'`);
200 |                 }
201 |
202 |                 prompt.push(suffix);
203 |
204 |                 try {
205 |                     const base64 = await imageProcessor.getBase64(image_id);
206 |
207 |                     if (!base64) {
208 |                         return {
209 |                             success: false,
210 |                             error: `Image ${image_id} Not Found`,
211 |                         };
212 |                     }
213 |
214 |                     const { text } = await chatModel.chat([
215 |                         {
216 |                             role: "user",
217 |                             content: [
218 |                                 { type: "text", text: prompt.join("\n") },
219 |                                 { type: "image_url", image_url: { detail: "auto", url: base64 } },
220 |                             ],
221 |                         },
222 |                     ]);
223 |
224 |                     return {
225 |                         success: true,
226 |                         result: text,
227 |                     };
228 |                 } catch (error) {
229 |                     return {
230 |                         success: false,
231 |                         error: `${error.name}: ${error.message}`,
232 |                     };
233 |                 }
234 |             },
235 |         });
236 |     }
237 | }

</file_content>
</file_content>

<file_content path="external/YesImBot/packages/core/src/services/worldstate">
├── DataManager.ts
├── interfaces.ts
├── model.ts
└── repositories/

<file_content path="external/YesImBot/packages/core/src/services/worldstate/DataManager.ts">
  1 | import { Context, randomId, Service, Session } from "koishi";
  2 | import { AgentResponse, Channel, MemberSummary, WorldState } from "./interfaces";
  3 | import { MemberRepository } from "./repositories/MemberRepository";
  4 | import { TurnRepository } from "./repositories/TurnRepository";
  5 | import { AgentResponseData, ChannelEventData, TurnData } from "./model";
  6 |
  7 | declare module "koishi" {
  8 |     interface Context {
  9 |         "yesimbot.data": DataManager;
 10 |     }
 11 | }
 12 |
 13 | export class DataManager extends Service {
 14 |     public readonly members: MemberRepository;
 15 |     public readonly turns: TurnRepository;
 16 |
 17 |     constructor(ctx: Context) {
 18 |         super(ctx, "yesimbot.data", true);
 19 |         // 应用所有数据库模型定义
 20 |         ctx.plugin(require("./model"));
 21 |
 22 |         // 初始化所有 repositories
 23 |         this.members = new MemberRepository(ctx);
 24 |         this.turns = new TurnRepository(ctx, this.members);
 25 |     }
 26 |
 27 |     /**
 28 |      * 获取一个频道的完整运行时对象
 29 |      */
 30 |     async getFullChannel(platform: string, channelId: string): Promise<Channel | null> {
 31 |         // 1. 获取基础频道数据
 32 |         const [channelData] = await this.ctx.database.get("channel", { platform, id: channelId });
 33 |         if (!channelData) return null;
 34 |
 35 |         // 2. 使用 Repositories 获取关联数据
 36 |         const members = await this.members.getFullMembers(platform, channelId);
 37 |         const history = await this.turns.getFullTurns(platform, channelId);
 38 |
 39 |         // 3. 组装 MemberSummary
 40 |         const memberSummary: MemberSummary = {
 41 |             total_count: channelData.totalMemberCount,
 42 |             recent_active_members_count: channelData.recentActiveCount,
 43 |             // online_count 是一个纯运行时状态，无法从数据库直接获取
 44 |             // 通常需要通过适配器API实时查询，或从心跳/presence事件中维护
 45 |             online_count: 0,
 46 |         };
 47 |
 48 |         // 4. 组合成最终的 Channel 对象
 49 |         return {
 50 |             id: channelData.id,
 51 |             platform: channelData.platform,
 52 |             name: channelData.name,
 53 |             type: channelData.type,
 54 |             meta: {
 55 |                 description: channelData.description,
 56 |             },
 57 |             members,
 58 |             history,
 59 |             memberSummary,
 60 |         };
 61 |     }
 62 |
 63 |     /**
 64 |      * 获取当前的世界状态
 65 |      */
 66 |     async getWorldState(): Promise<WorldState> {
 67 |         const activeThreshold = new Date(Date.now() - 1 * 60 * 60 * 1000);
 68 |
 69 |         const allChannels = await this.ctx.database.get("channel", {});
 70 |
 71 |         const activeChannelPromises: Promise<Channel>[] = [];
 72 |         const inactiveChannelPromises: Promise<Channel>[] = [];
 73 |
 74 |         for (const chan of allChannels) {
 75 |             const promise = this.getFullChannel(chan.platform, chan.id);
 76 |             if (chan.lastActivityAt > activeThreshold) {
 77 |                 activeChannelPromises.push(promise);
 78 |             } else {
 79 |                 inactiveChannelPromises.push(promise);
 80 |             }
 81 |         }
 82 |
 83 |         return {
 84 |             timestamp: new Date().toISOString(),
 85 |             activeChannels: (await Promise.all(activeChannelPromises)).filter((c) => c !== null),
 86 |             inactiveChannels: (await Promise.all(inactiveChannelPromises)).filter((c) => c !== null),
 87 |         };
 88 |     }
 89 |
 90 |     /**
 91 |      * 开始一个新的对话回合 (Turn)。
 92 |      * @param platform - 平台
 93 |      * @param channelId - 频道ID
 94 |      * @returns 返回新创建的 TurnData 对象。
 95 |      */
 96 |     async startNewTurn(platform: string, channelId: string): Promise<TurnData> {
 97 |         const newTurn: Partial<TurnData> = {
 98 |             id: randomId(),
 99 |             platform,
100 |             channelId,
101 |             status: "new",
102 |             summary: "",
103 |             startTimestamp: new Date(),
104 |             endTimestamp: null, // 尚未结束
105 |         };
106 |         return await this.ctx.database.create("turns", newTurn);
107 |     }
108 |
109 |     /**
110 |      * 向指定的 Turn 添加一条消息事件。
111 |      * @param turnId - 目标 Turn 的 ID
112 |      * @param session - 触发消息的 Koishi Session 对象
113 |      * @returns 返回新创建的 ChannelEventData 对象。
114 |      */
115 |     async addMessageEvent(turnId: string, session: Session): Promise<ChannelEventData> {
116 |         return await this.ctx.database.create("channel_events", {
117 |             turnId,
118 |             type: "message_sent",
119 |             timestamp: new Date(session.timestamp),
120 |             data: {
121 |                 messageId: session.messageId,
122 |                 senderId: session.userId, // 这是平台ID (pid)
123 |                 content: session.content,
124 |             },
125 |         });
126 |     }
127 |
128 |     /**
129 |      * 向指定的 Turn 添加一个通用事件。
130 |      * @param turnId - 目标 Turn 的 ID
131 |      * @param type - 事件类型
132 |      * @param data - 事件的特定数据
133 |      * @returns 返回新创建的 ChannelEventData 对象。
134 |      */
135 |     async addGenericEvent(turnId: string, type: string, data: object): Promise<ChannelEventData> {
136 |         return await this.ctx.database.create("channel_events", {
137 |             turnId,
138 |             type,
139 |             timestamp: new Date(),
140 |             data,
141 |         });
142 |     }
143 |
144 |     /**
145 |      * 结束一个对话回合。
146 |      * @param turnId - 要结束的 Turn 的 ID
147 |      * @param summary - (可选) 对该回合的AI摘要
148 |      */
149 |     async endTurn(turnId: string, summary?: string): Promise<void> {
150 |         await this.ctx.database.upsert("turns", [
151 |             {
152 |                 id: turnId,
153 |                 status: summary ? "summarized" : "full",
154 |                 summary: summary,
155 |                 endTimestamp: new Date(),
156 |             },
157 |         ]);
158 |     }
159 |
160 |     /**
161 |      * 向指定的 Turn 添加一个完整的 Agent 响应。
162 |      * @param turnId - 目标 Turn 的 ID
163 |      * @param response - AgentResponse 业务对象
164 |      * @returns 返回新创建的 AgentResponseData 对象。
165 |      */
166 |     async addAgentResponse(turnId: string, response: AgentResponse): Promise<AgentResponseData> {
167 |         const { thoughts, actions, observations } = response;
168 |         return await this.ctx.database.create("agent_responses", {
169 |             turnId,
170 |             thoughts,
171 |             actions,
172 |             observations,
173 |         });
174 |     }
175 |
176 |     /**
177 |      * 根据 Session "顺便" 更新频道信息。
178 |      * 这应该在每次收到消息时调用。
179 |      * @param session - Koishi Session 对象
180 |      */
181 |     async touchChannel(session: Session): Promise<void> {
182 |         // session.guild 包含适配器获取的最新群组信息
183 |         const channelName = session.guild?.name ?? session.channel.name;
184 |
185 |         await this.ctx.database.upsert("channel", [
186 |             {
187 |                 id: session.channelId,
188 |                 platform: session.platform,
189 |                 // 更新频道名称，以防它被修改
190 |                 name: channelName,
191 |                 // 更新最后活动时间
192 |                 lastActivityAt: new Date(),
193 |             },
194 |         ]);
195 |     }
196 |
197 |     /**
198 |      * 当成员列表发生变化时，更新频道的成员计数值。
199 |      * 这应该在 'guild-member-added' 或 'guild-member-removed' 事件中调用。
200 |      * @param guildId - 频道/群组 ID
201 |      * @param platform - 平台
202 |      */
203 |     async updateChannelMemberCount(guildId: string, platform: string) {
204 |         // 尝试从适配器获取最新的成员总数
205 |         const bot = this.ctx.bots[`${platform}:${this.ctx.config.selfId}`];
206 |         let totalCount = 0;
207 |         try {
208 |             // 注意: getGuild 方法和返回的成员数取决于具体适配器
209 |             const guild = await bot?.getGuild(guildId);
210 |             totalCount = guild?.memberCount ?? 0;
211 |         } catch (error) {
212 |             // 如果API调用失败，可以从我们自己的 members 表中计数作为备用方案
213 |             totalCount = await this.ctx.database.count("members", { channelId: guildId, platform });
214 |             this.ctx.logger("data").warn(`Failed to fetch member count for ${guildId}, using fallback count: ${totalCount}`);
215 |         }
216 |
217 |         await this.ctx.database.upsert("channel", [
218 |             {
219 |                 id: guildId,
220 |                 platform: platform,
221 |                 totalMemberCount: totalCount,
222 |             },
223 |         ]);
224 |     }
225 | }

</file_content>

<file_content path="external/YesImBot/packages/core/src/services/worldstate/interfaces.ts">
  1 | export interface WorldState {
  2 |     timestamp: string;
  3 |     activeChannels: Channel[];
  4 |     inactiveChannels: Channel[];
  5 | }
  6 |
  7 | // 一个频道对象，替换原来的 Scenario
  8 | // 群组或是私聊，或者沙盒测试环境
  9 | export interface Channel {
 10 |     id: string; // 频道 ID
 11 |     name: string; // 频道名称，群聊就是群组名，私聊为“你和 <用户名> 的私聊”
 12 |     type: "guild" | "private" | "sandbox";
 13 |     platform: string;
 14 |     meta: {
 15 |         description?: string; // 频道描述，有些适配器获取不到。或许可以根据历史对话生成一个
 16 |     };
 17 |     // 经过智能筛选和摘要的成员信息
 18 |     // 层次1: 核心成员，如群主、管理员，或与自己有特殊关系的成员
 19 |     // 层次2: 上下文相关成员 (近期发言或被@)
 20 |     members: Member[];
 21 |
 22 |     // 层次3: 群体氛围感知的摘要信息
 23 |     memberSummary: MemberSummary;
 24 |     history: Turn[];
 25 | }
 26 |
 27 | export interface MemberSummary {
 28 |     total_count: number; // 频道成员总数
 29 |     online_count: number; // 频道在线成员数
 30 |     recent_active_members_count: number; // 频道近期活跃成员数
 31 | }
 32 |
 33 | export interface User {
 34 |     id: string; // 特点平台用户 ID (pid)
 35 |     name: string; // 用户名称
 36 |     meta: {
 37 |         avatar?: string; // 用户头像 URL
 38 |         [key: string]: unknown;
 39 |     };
 40 |     created_at: Date;
 41 |     updated_at: Date;
 42 | }
 43 |
 44 | export interface Member extends User {
 45 |     channel_id: string;
 46 |     meta: User["meta"] & {
 47 |         nick?: string;
 48 |         role?: string;
 49 |     };
 50 |     last_active?: string; // 用户上次活跃时间
 51 | }
 52 |
 53 | export interface Turn {
 54 |     id: string;
 55 |     status: "full" | "summarized" | "folded" | "new";
 56 |     events: ChannelEvent[];
 57 |     summary?: string; // 摘要
 58 |     responses: AgentResponse[];
 59 | }
 60 |
 61 | export interface AgentResponse {
 62 |     thoughts: Thought;
 63 |     actions: Action[];
 64 |     observations: ActionResult[];
 65 | }
 66 |
 67 | export interface Thought {
 68 |     obverse: string;
 69 |     analyze_infer: string;
 70 |     plan: string;
 71 | }
 72 |
 73 | export interface Action {
 74 |     function: string;
 75 |     params: Record<string, unknown>;
 76 | }
 77 |
 78 | export interface ActionResult {
 79 |     function: string;
 80 |     result: {
 81 |         success: boolean;
 82 |         result?: unknown;
 83 |         error?: unknown;
 84 |     };
 85 | }
 86 |
 87 | // --- 事件相关接口 ---
 88 |
 89 | // 基础事件结构
 90 | interface BaseEvent {
 91 |     id: number; // 自增 ID
 92 |     type: string;
 93 |     timestamp: Date;
 94 | }
 95 |
 96 | // 具体事件类型定义
 97 | export interface UserJoinedEvent extends BaseEvent {
 98 |     type: "user_joined";
 99 |     actor: Member; // 操作者 (可能是系统或其他成员)
100 |     user: Member; // 加入的成员
101 |     note?: string;
102 | }
103 |
104 | export interface UserLeftEvent extends BaseEvent {
105 |     type: "user_left";
106 |     actor: Member;
107 |     user: Member;
108 |     reason?: string;
109 | }
110 |
111 | export interface MessageSentEvent extends BaseEvent {
112 |     type: "message_sent";
113 |     messageId: string;
114 |     sender: Member;
115 |     content: string;
116 | }
117 |
118 | export interface SystemNotificationEvent extends BaseEvent {
119 |     type: "system_notification";
120 |     content: string;
121 | }
122 |
123 | export type ChannelEvent = UserJoinedEvent | UserLeftEvent | MessageSentEvent | SystemNotificationEvent;

</file_content>

<file_content path="external/YesImBot/packages/core/src/services/worldstate/model.ts">
  1 | import { Context } from "koishi";
  2 | import { Action, ActionResult, Thought, Turn } from "./interfaces";
  3 |
  4 | // --- Data Transfer Objects (DTOs) / Database Table Schemas ---
  5 | // 这些接口精确匹配数据库中的一行数据
  6 |
  7 | export interface MemberData {
  8 |     userId: number;
  9 |     platform: string;
 10 |     channelId: string;
 11 |     nick: string;
 12 |     role: string;
 13 |     lastActive: Date;
 14 | }
 15 |
 16 | export interface TurnData {
 17 |     id: string;
 18 |     channelId: string;
 19 |     platform: string;
 20 |     status: Turn["status"];
 21 |     summary: string;
 22 |     startTimestamp: Date;
 23 |     endTimestamp: Date;
 24 | }
 25 |
 26 | export interface AgentResponseData {
 27 |     id: number;
 28 |     turnId: string;
 29 |     thoughts: Thought;
 30 |     actions: Action[];
 31 |     observations: ActionResult[];
 32 | }
 33 |
 34 | export interface ChannelEventData {
 35 |     id: number; // 自增主键，用于唯一标识和排序
 36 |     turnId: string; // 外键，关联到 Turn
 37 |     type: string; // 事件类型，如 'user_joined', 'message_sent'
 38 |     timestamp: Date; // 事件发生时间
 39 |     data: object; // JSON 字段，存储该事件类型的特定数据
 40 | }
 41 |
 42 | // --- Koishi-specific Table Augmentation ---
 43 | // 扩展 Koishi 的核心接口和表定义
 44 |
 45 | declare module "koishi" {
 46 |     interface User {
 47 |         avatar?: string;
 48 |         createdAt: Date;
 49 |         updatedAt: Date;
 50 |     }
 51 |
 52 |     interface Channel {
 53 |         name?: string;
 54 |         type?: "guild" | "private" | "sandbox";
 55 |         description?: string;
 56 |         totalMemberCount?: number;
 57 |         recentActiveCount?: number;
 58 |         lastActivityAt?: Date;
 59 |     }
 60 |
 61 |     interface Tables {
 62 |         members: MemberData;
 63 |         turns: TurnData;
 64 |         channel_events: ChannelEventData;
 65 |         agent_responses: AgentResponseData;
 66 |     }
 67 | }
 68 |
 69 | export const name = "yesimbot-models";
 70 | export const inject = ["database"];
 71 |
 72 | export function apply(ctx: Context) {
 73 |     ctx.model.extend("user", {
 74 |         avatar: "string",
 75 |         createdAt: "timestamp",
 76 |         updatedAt: "timestamp",
 77 |     });
 78 |
 79 |     ctx.model.extend("channel", {
 80 |         name: "string",
 81 |         type: "string",
 82 |         description: "text",
 83 |         totalMemberCount: "unsigned",
 84 |         recentActiveCount: "unsigned",
 85 |         lastActivityAt: "timestamp",
 86 |     });
 87 |
 88 |     ctx.model.extend(
 89 |         "members",
 90 |         {
 91 |             userId: "unsigned",
 92 |             platform: "string(255)",
 93 |             channelId: "string(255)",
 94 |             nick: "string",
 95 |             role: "string",
 96 |             lastActive: "timestamp",
 97 |         },
 98 |         {
 99 |             primary: ["userId", "platform", "channelId"],
100 |             foreign: {
101 |                 userId: ["user", "id"],
102 |                 channelId: ["channel", "id"],
103 |                 platform: ["channel", "platform"],
104 |             },
105 |         }
106 |     );
107 |
108 |     ctx.model.extend(
109 |         "channel_events",
110 |         {
111 |             id: "unsigned",
112 |             turnId: "string(64)",
113 |             type: "string(64)",
114 |             timestamp: "timestamp",
115 |             data: "json",
116 |         },
117 |         {
118 |             autoInc: true,
119 |             primary: "id",
120 |             foreign: {
121 |                 turnId: ["turns", "id"],
122 |             },
123 |         }
124 |     );
125 |
126 |     ctx.model.extend(
127 |         "turns",
128 |         {
129 |             id: "char(64)",
130 |             channelId: "char(64)",
131 |             platform: "char(64)",
132 |             status: "string",
133 |             summary: "text",
134 |             startTimestamp: "timestamp",
135 |             endTimestamp: "timestamp",
136 |         },
137 |         {
138 |             primary: "id",
139 |             foreign: {
140 |                 channelId: ["channel", "id"],
141 |                 platform: ["channel", "platform"],
142 |             },
143 |         }
144 |     );
145 |
146 |     ctx.model.extend(
147 |         "agent_responses",
148 |         {
149 |             id: "unsigned",
150 |             turnId: "char(64)",
151 |             thoughts: "json",
152 |             actions: "json",
153 |             observations: "json",
154 |         },
155 |         {
156 |             autoInc: true,
157 |             primary: "id",
158 |             foreign: {
159 |                 turnId: ["turns", "id"],
160 |             },
161 |         }
162 |     );
163 | }

</file_content>
</file_content>

<file_content path="external/YesImBot/packages/core/src/prompt/PromptBuilder.ts">
  1 | import { readFileSync } from "fs";
  2 | import { Context } from "koishi";
  3 | import path from "path";
  4 | import type { Part, TextPart, UserMessagePart } from "xsai";
  5 |
  6 | import { message } from "../dependencies/xsai";
  7 | import ToolManager from "../extensions";
  8 | import { MemoryService } from "../memory/MemoryService";
  9 | import { MessageContext } from "../middleware/base";
 10 | import { formatDate } from "../utils";
 11 | import { MultimodalConfig } from "../services/scenario/Scenario";
 12 | import { ScenarioManager } from "../services/scenario/ScenarioManager";
 13 |
 14 | const { textPart } = message;
 15 |
 16 | export type PromptBlockGenerator = (ctx: MessageContext, PromptBuilder: PromptBuilder) => Promise<string | Array<Part> | null>;
 17 |
 18 | export interface PromptBuilderConfig {
 19 |     SystemTemplate: string;
 20 |     UserTemplate: string;
 21 |     ToolTemplate: string;
 22 | }
 23 |
 24 | export class PromptBuilder {
 25 |     private templates: Map<string, string> = new Map();
 26 |     private blockGenerators: Map<string, PromptBlockGenerator> = new Map();
 27 |
 28 |     private memory: MemoryService;
 29 |     private toolManager: ToolManager;
 30 |     private logger: any;
 31 |     private multimodalConfig: MultimodalConfig; // 新增多模态配置
 32 |
 33 |     // 用于在构建完整 Prompt 时追踪图片总数
 34 |     private _currentPromptImageCount: number = 0;
 35 |
 36 |     constructor(
 37 |         readonly ctx: Context,
 38 |         private readonly scenarioManager: ScenarioManager,
 39 |         private readonly config: PromptBuilderConfig,
 40 |         multimodalConfig: MultimodalConfig // 接收多模态配置
 41 |     ) {
 42 |         this.config = config;
 43 |         this.multimodalConfig = multimodalConfig;
 44 |
 45 |         this.logger = ctx.logger("PromptBuilder");
 46 |
 47 |         this.memory = ctx["yesimbot.memory"];
 48 |         this.toolManager = ctx["yesimbot.tool"];
 49 |
 50 |         // 注册默认的提示词块生成器
 51 |         this.registerDefaultBlockGenerators();
 52 |     }
 53 |
 54 |     /**
 55 |      * 注册一个自定义模板（允许运行时覆盖或添加模板）。
 56 |      * @param name 模板文件的名称，例如 'my_custom_template.txt'
 57 |      * @param content 模板内容字符串
 58 |      */
 59 |     public registerTemplate(name: string, content: string): void {
 60 |         this.templates.set(name, content);
 61 |         this.logger.debug(`Registered custom template: ${name}`);
 62 |     }
 63 |
 64 |     /**
 65 |      * 注册一个用于生成特定提示词块内容的函数。
 66 |      * @param name 块的名称，对应模板中的占位符，例如 'memory', 'tools'
 67 |      * @param generator 生成器函数，接收 MessageContext 和 PromptBuilder 实例作为参数，返回字符串、Part 数组或 null。
 68 |      */
 69 |     public registerBlockGenerator(name: string, generator: PromptBlockGenerator): void {
 70 |         if (this.blockGenerators.has(name)) {
 71 |             this.logger.warn(`Overwriting existing prompt block generator: ${name}`);
 72 |         }
 73 |         this.blockGenerators.set(name, generator);
 74 |         this.logger.debug(`Registered prompt block generator: ${name}`);
 75 |     }
 76 |
 77 |     /**
 78 |      * 注册默认的提示词块生成器。
 79 |      */
 80 |     private registerDefaultBlockGenerators(): void {
 81 |         // 核心记忆块 (通常为纯文本)
 82 |         this.registerBlockGenerator("CORE_MEMORY", async () => {
 83 |             return this.memory.getCoreMemoryContentForPrompt();
 84 |         });
 85 |
 86 |         // 工具描述块 (通常为纯文本)
 87 |         this.registerBlockGenerator("TOOL_INSTRUCTION", async () => {
 88 |             const template = this.config.ToolTemplate;
 89 |             if (!template) {
 90 |                 this.logger.warn("Tool template not found.");
 91 |                 return "Please respond appropriately.";
 92 |             }
 93 |             return template;
 94 |         });
 95 |
 96 |         // 工具定义块 (通常为纯文本)
 97 |         this.registerBlockGenerator("TOOL_DEFINITION", async () => {
 98 |             return this.toolManager.getToolPrompts();
 99 |         });
100 |
101 |         // // 任务指令块 (通常为纯文本)
102 |         // this.registerBlockGenerator("TASK_INSTRUCTION", async () => {
103 |         //     const template = this.templates.get("task_instruction.txt");
104 |         //     if (!template) {
105 |         //         this.logger.warn("task_instruction.txt template not found.");
106 |         //         return "Please respond appropriately.";
107 |         //     }
108 |         //     return template;
109 |         // });
110 |
111 |         // 场景上下文生成器 (可能包含文本和图片)
112 |         this.registerBlockGenerator("SCENARIO_CONTEXT", async (ctx) => {
113 |             // 获取所有活跃和不活跃的群组场景
114 |             const activeScenarios = this.scenarioManager.getActiveScenariosForRender(ctx.allowedChannels);
115 |             const inactiveScenarios = this.scenarioManager.getInactiveScenariosForRender(ctx.allowedChannels);
116 |
117 |             let allScenarioParts: Array<UserMessagePart> = [];
118 |
119 |             // 辅助函数：将文本合并到 Parts 数组中，并尝试合并连续的 TextPart
120 |             const appendToScenarioParts = (partsToAdd: Array<Part>) => {
121 |                 for (const part of partsToAdd) {
122 |                     if (part.type === "text") {
123 |                         if (allScenarioParts.length > 0 && allScenarioParts[allScenarioParts.length - 1].type === "text") {
124 |                             (allScenarioParts[allScenarioParts.length - 1] as TextPart).text += `\n${(part as TextPart).text}`;
125 |                         } else {
126 |                             allScenarioParts.push(part);
127 |                         }
128 |                     } else if (part.type === "image_url") {
129 |                         allScenarioParts.push(part); // 图片直接添加
130 |                     }
131 |                 }
132 |             };
133 |
134 |             const scenarioUpdateTime = formatDate(new Date());
135 |             appendToScenarioParts([textPart(`<scenario_update timestamp="${scenarioUpdateTime}">`)]);
136 |
137 |             if (activeScenarios.length > 0) {
138 |                 for (const s of activeScenarios) {
139 |                     const renderedParts = await s.render();
140 |                     appendToScenarioParts(renderedParts);
141 |                 }
142 |             } else {
143 |                 appendToScenarioParts([textPart(`  <!-- No active scenarios with new messages -->`)]);
144 |             }
145 |             appendToScenarioParts([textPart(`</scenario_update>`)]);
146 |
147 |             appendToScenarioParts([textPart(`<no_activity>`)]);
148 |             if (inactiveScenarios.length > 0) {
149 |                 for (const s of inactiveScenarios) {
150 |                     const renderedParts = await s.render();
151 |                     appendToScenarioParts(renderedParts);
152 |                 }
153 |             } else {
154 |                 appendToScenarioParts([textPart(`  <!-- No inactive scenarios to report -->`)]);
155 |             }
156 |             appendToScenarioParts([textPart(`</no_activity>`)]);
157 |
158 |             return allScenarioParts;
159 |         });
160 |     }
161 |
162 |     /**
163 |      * 构建总体的系统提示词（LLM的system role）。
164 |      * 包含基础设定、核心记忆、工具说明等。
165 |      * @param ctx 消息上下文。
166 |      * @returns 完整的系统提示词字符串。
167 |      */
168 |     public async buildSystemPrompt(ctx: MessageContext): Promise<string> {
169 |         let systemTemplate = this.config.SystemTemplate;
170 |
171 |         // 替换所有已注册的块
172 |         for (const [blockName, generator] of this.blockGenerators) {
173 |             const placeholder = `{{${blockName}}}`;
174 |             if (systemTemplate.includes(placeholder)) {
175 |                 const content = await generator(ctx, this);
176 |                 if (typeof content === "string" && content !== null) {
177 |                     // 系统提示词只支持文本
178 |                     systemTemplate = systemTemplate.replace(placeholder, content);
179 |                 } else if (Array.isArray(content)) {
180 |                     this.logger.warn(
181 |                         `Prompt block generator '${blockName}' returned array of parts, but System Prompt only supports text. Converting to string.`
182 |                     );
183 |                     // 将数组转换为字符串表示，避免丢失信息
184 |                     systemTemplate = systemTemplate.replace(placeholder, this.flattenPartsToString(content));
185 |                 } else {
186 |                     systemTemplate = systemTemplate.replace(placeholder, "");
187 |                 }
188 |             }
189 |         }
190 |
191 |         // 清理可能剩余的未替换的占位符（例如，如果某个块生成器返回null，但模板中包含该块）
192 |         systemTemplate = systemTemplate.replace(/\{\{[a-zA-Z0-9_]+\}\}/g, "");
193 |
194 |         return systemTemplate;
195 |     }
196 |
197 |     /**
198 |      * 构建总体的用户提示词（LLM的user role）。
199 |      * 包含当前会话上下文、最新消息、场景更新等。
200 |      * @param ctx 消息上下文。
201 |      * @returns 完整的用户提示词 Part 数组。
202 |      */
203 |     public async buildUserPrompt(ctx: MessageContext): Promise<Array<UserMessagePart>> {
204 |         const userPromptParts: Array<UserMessagePart> = [];
205 |         this._currentPromptImageCount = 0; // 重置本次 Prompt 的图片计数
206 |
207 |         const userTemplateString = this.config.UserTemplate;
208 |         let lastIndex = 0;
209 |         const placeholderRegex = /\{\{([a-zA-Z0-9_]+)\}\}/g; // 匹配所有 {{placeholder}}
210 |
211 |         let match;
212 |         while ((match = placeholderRegex.exec(userTemplateString)) !== null) {
213 |             const placeholderName = match[1];
214 |             const placeholderStart = match.index;
215 |             const placeholderEnd = match.index + match[0].length;
216 |
217 |             // 添加占位符之前的文本内容
218 |             if (placeholderStart > lastIndex) {
219 |                 this.appendPart(userPromptParts, textPart(userTemplateString.substring(lastIndex, placeholderStart)));
220 |             }
221 |
222 |             // 获取占位符生成的内容
223 |             const generator = this.blockGenerators.get(placeholderName);
224 |             if (generator) {
225 |                 const generatedContent = await generator(ctx, this);
226 |
227 |                 if (generatedContent) {
228 |                     if (Array.isArray(generatedContent)) {
229 |                         // 如果生成器返回 Part 数组 (如 scenario_context)
230 |                         for (const part of generatedContent) {
231 |                             if (part.type === "image_url") {
232 |                                 if (
233 |                                     this.multimodalConfig.Enabled &&
234 |                                     this._currentPromptImageCount < this.multimodalConfig.MaxImagesPerPrompt
235 |                                 ) {
236 |                                     this.appendPart(userPromptParts, part);
237 |                                     this._currentPromptImageCount++;
238 |                                 } else {
239 |                                     // 超出总图片限制，图片退化为文本
240 |                                     this.appendPart(userPromptParts, textPart(`[图片已超出Prompt限制，未显示] `));
241 |                                 }
242 |                             } else {
243 |                                 this.appendPart(userPromptParts, part); // 文本部分直接添加
244 |                             }
245 |                         }
246 |                     } else if (typeof generatedContent === "string") {
247 |                         // 如果生成器返回字符串
248 |                         this.appendPart(userPromptParts, textPart(generatedContent));
249 |                     }
250 |                 }
251 |             }
252 |             lastIndex = placeholderEnd;
253 |         }
254 |
255 |         // 添加最后一个占位符之后的文本内容
256 |         if (lastIndex < userTemplateString.length) {
257 |             this.appendPart(userPromptParts, textPart(userTemplateString.substring(lastIndex)));
258 |         }
259 |
260 |         return userPromptParts;
261 |     }
262 |
263 |     /**
264 |      * 辅助函数：将 Part 添加到数组中，并尝试合并连续的 TextPart
265 |      * @param parts 目标 Part 数组
266 |      * @param newPart 要添加的 Part
267 |      */
268 |     private appendPart(parts: Array<Part>, newPart: Part): void {
269 |         if (newPart.type === "text" && parts.length > 0 && parts[parts.length - 1].type === "text") {
270 |             // 合并连续的 TextPart
271 |             (parts[parts.length - 1] as TextPart).text += (newPart as TextPart).text;
272 |         } else {
273 |             parts.push(newPart);
274 |         }
275 |     }
276 |
277 |     /**
278 |      * 辅助函数：将 Part 数组扁平化为字符串 (用于系统提示词不支持多模态的情况)
279 |      * @param parts Parts 数组
280 |      * @returns 扁平化后的字符串
281 |      */
282 |     private flattenPartsToString(parts: Array<Part>): string {
283 |         let result = "";
284 |         for (const part of parts) {
285 |             if (part.type === "text") {
286 |                 result += (part as TextPart).text;
287 |             } else if (part.type === "image_url") {
288 |                 result += `[图片(未显示)]`; // 或更详细的描述
289 |             }
290 |             // 忽略其他类型或处理未知类型
291 |         }
292 |         return result.trim();
293 |     }
294 | }
295 |
296 | export const SystemBaseTemplate = readFileSync(path.resolve(__dirname, "../../resources/prompts/memgpt_v2_chat.txt"), "utf-8");
297 | export const ToolBaseTemplate = readFileSync(path.resolve(__dirname, "../../resources/prompts/tool_base.txt"), "utf-8");
298 | export const UserBaseTemplate = readFileSync(path.resolve(__dirname, "../../resources/prompts/user_base.txt"), "utf-8");

</file_content>

<file_content path="external/yesimbot/packages/core/src/agent.ts">
  1 | import { Context } from "koishi";
  2 | import { Config } from "./config";
  3 | import { MessageContext, MiddlewareManager } from "./middleware/base";
  4 | import { DatabaseManager } from "./services/DatabaseManager";
  5 | import { MiddlewareConfigurator } from "./services/MiddlewareConfigurator";
  6 | import { ServiceContainer } from "./services/ServiceContainer";
  7 | import { ServiceInitializer } from "./services/ServiceInitializer";
  8 |
  9 | declare module "koishi" {
 10 |     interface Events {
 11 |         "scenario/clear": (channelId: string) => void;
 12 |         "scenario/clearAll": () => void;
 13 |         "channel:processing:release": (channelId: string) => void;
 14 |     }
 15 | }
 16 |
 17 | /**
 18 |  * Agent 核心类
 19 |  * 负责协调各个组件，实现主要的消息处理流程
 20 |  */
 21 | export default class AgentCore {
 22 |     private ctx: Context;
 23 |     private config: Config;
 24 |
 25 |     private container: ServiceContainer;
 26 |     private databaseManager: DatabaseManager;
 27 |     private serviceInitializer: ServiceInitializer;
 28 |     private middlewareConfigurator: MiddlewareConfigurator;
 29 |     private middlewareManager: MiddlewareManager;
 30 |
 31 |     static readonly name = "yesimbot";
 32 |     static readonly inject = ["yesimbot.tool", "yesimbot.memory", "yesimbot.model"];
 33 |
 34 |     constructor(ctx: Context, config: Config) {
 35 |         this.ctx = ctx;
 36 |         this.config = config;
 37 |
 38 |         // 初始化组件
 39 |         this.container = new ServiceContainer();
 40 |         this.databaseManager = new DatabaseManager(ctx);
 41 |         this.serviceInitializer = new ServiceInitializer(ctx, config, this.container);
 42 |         this.middlewareConfigurator = new MiddlewareConfigurator(ctx, config, this.container);
 43 |
 44 |         ctx.on("ready", async () => {
 45 |             await this.initialize();
 46 |         });
 47 |     }
 48 |
 49 |     /**
 50 |      * 初始化 Agent
 51 |      */
 52 |     private async initialize(): Promise<void> {
 53 |         try {
 54 |             // 1. 注册数据库
 55 |             this.databaseManager.registerTables();
 56 |
 57 |             // 2. 初始化服务
 58 |             await this.serviceInitializer.initialize();
 59 |
 60 |             // 3. 配置中间件
 61 |             this.middlewareManager = this.middlewareConfigurator.configure();
 62 |
 63 |             // 4. 注册消息处理中间件
 64 |             this.registerMessageHandler();
 65 |
 66 |             this.ctx.logger.info("[Agent] 初始化完成");
 67 |         } catch (error) {
 68 |             this.ctx.logger.error("[Agent] 初始化失败:", error);
 69 |             throw error;
 70 |         }
 71 |     }
 72 |
 73 |     /**
 74 |      * 注册 Koishi 消息处理中间件
 75 |      */
 76 |     private registerMessageHandler(): void {
 77 |         this.ctx.middleware(async (session, next) => {
 78 |             try {
 79 |                 const allowedChannels = this.config.ReplyCondition.Channels.find((slots) => slots.includes(session.channelId)) || [];
 80 |
 81 |                 if (allowedChannels.length === 0) {
 82 |                     if (this.config.Debug.EnableDebug) {
 83 |                         this.ctx.logger.info(`${session.channelId} 不在回复列表，已跳过`);
 84 |                     }
 85 |                     return next();
 86 |                 }
 87 |
 88 |                 // 创建消息上下文
 89 |                 const messageContext = new MessageContext(this.ctx, session, allowedChannels);
 90 |
 91 |                 // 执行中间件链
 92 |                 await this.middlewareManager.execute(messageContext);
 93 |
 94 |                 // 继续 Koishi 中间件链
 95 |                 return next();
 96 |             } catch (error) {
 97 |                 this.ctx.logger.error("[Agent] 消息处理错误:", (error as Error).message);
 98 |                 if (this.config.Debug.EnableDebug) {
 99 |                     this.ctx.logger.error((error as Error).stack);
100 |                 }
101 |                 return next();
102 |             }
103 |         });
104 |     }
105 |
106 |     /**
107 |      * 清理资源
108 |      */
109 |     public dispose(): void {
110 |         this.middlewareConfigurator.dispose();
111 |         this.container.dispose();
112 |         this.ctx.logger.info("[Agent] 资源清理完成");
113 |     }
114 | }

</file_content>
