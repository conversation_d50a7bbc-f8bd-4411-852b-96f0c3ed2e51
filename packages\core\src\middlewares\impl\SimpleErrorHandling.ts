import { Context } from "koishi";
import { v4 as uuidv4 } from "uuid";
import { SimpleMiddleware } from "../SimpleMiddleware";
import { MessageContext } from "../base";

/**
 * 错误处理配置
 */
export interface ErrorHandlingConfig {
    debug?: boolean;
    uploadDump?: boolean;
    pasteServiceUrl?: string;
}

/**
 * 简化的错误处理中间件
 */
export class SimpleErrorHandlingMiddleware extends SimpleMiddleware {
    constructor(
        ctx: Context,
        private config: ErrorHandlingConfig = {}
    ) {
        super("error-handling", ctx);
    }

    async execute(ctx: MessageContext, next: () => Promise<void>): Promise<void> {
        try {
            await next();
        } catch (error) {
            await this.handleError(error as Error, ctx);
            // 不重新抛出错误，避免影响其他中间件
        }
    }

    private async handleError(error: Error, ctx: MessageContext): Promise<void> {
        const errorId = uuidv4();
        
        // 基础错误日志
        this.logger.error(`[${errorId}] 处理消息时发生错误: ${error.message}`);
        
        if (this.config.debug) {
            this.logger.error(`[${errorId}] 错误堆栈:`, error.stack);
        }

        // 记录上下文信息
        if (ctx.koishiSession) {
            this.logger.error(`[${errorId}] 用户: ${ctx.koishiSession.userId}, 频道: ${ctx.koishiSession.channelId}`);
        }

        // 可选的错误上传
        if (this.config.uploadDump) {
            await this.uploadErrorDump(errorId, error, ctx);
        }
    }

    private async uploadErrorDump(errorId: string, error: Error, ctx: MessageContext): Promise<void> {
        try {
            const dump = this.createErrorDump(errorId, error, ctx);
            const url = await this.uploadToPasteService(dump);
            if (url) {
                this.logger.info(`[${errorId}] 错误报告已上传: ${url}`);
            }
        } catch (uploadError) {
            this.logger.warn(`[${errorId}] 上传错误报告失败: ${uploadError.message}`);
        }
    }

    private createErrorDump(errorId: string, error: Error, ctx: MessageContext): string {
        const dump = {
            errorId,
            timestamp: new Date().toISOString(),
            error: {
                name: error.name,
                message: error.message,
                stack: error.stack,
            },
            context: {
                userId: ctx.koishiSession?.userId,
                channelId: ctx.koishiSession?.channelId,
                platform: ctx.koishiSession?.platform,
                messageContent: ctx.koishiSession?.content,
            },
            system: {
                nodeVersion: process.version,
                platform: process.platform,
                arch: process.arch,
            },
        };
        
        return JSON.stringify(dump, null, 2);
    }

    private async uploadToPasteService(content: string): Promise<string | null> {
        if (!this.config.pasteServiceUrl) return null;
        
        try {
            const response = await fetch(this.config.pasteServiceUrl, {
                method: 'POST',
                headers: { 'Content-Type': 'text/plain' },
                body: content,
            });
            
            if (response.ok) {
                return response.url;
            }
        } catch (error) {
            this.logger.debug('上传到粘贴服务失败:', error);
        }
        
        return null;
    }
}

/**
 * 错误处理中间件工厂函数
 */
export function createErrorHandlingMiddleware(ctx: Context, config: ErrorHandlingConfig) {
    return new SimpleErrorHandlingMiddleware(ctx, config);
}
