import { Context } from "koishi";
import { Config } from "../config";
import { MiddlewareManager } from "../middlewares/base";
import { CheckReplyCondition } from "../middlewares/impl/CheckReplyCondition";
import { DatabaseStorageMiddleware } from "../middlewares/impl/DatabaseStorage";
import { ErrorHandlingMiddleware } from "../middlewares/impl/ErrorHandling";
import { LLMProcessingMiddleware } from "../middlewares/impl/LLMProcessing";
import { ResponseHandlingMiddleware } from "../middlewares/impl/ResponseHandling";
import { SimpleServiceManager } from "./SimpleServiceManager";

/**
 * 简化的中间件配置器
 * 直接配置中间件链，减少抽象层次
 */
export class SimpleMiddlewareConfigurator {
    constructor(
        private ctx: Context,
        private config: Config,
        private services: SimpleServiceManager
    ) {}

    /**
     * 配置并返回中间件管理器
     */
    configure(): MiddlewareManager {
        const middlewareManager = this.services.getMiddlewareManager();
        
        // 按顺序添加中间件
        this.addErrorHandling(middlewareManager);
        this.addDatabaseStorage(middlewareManager);
        this.addReplyCondition(middlewareManager);
        this.addLLMProcessing(middlewareManager);
        this.addResponseHandling(middlewareManager);
        
        // 注册清理处理器
        this.registerCleanupHandlers(middlewareManager);
        
        return middlewareManager;
    }

    private addErrorHandling(manager: MiddlewareManager): void {
        manager.use(new ErrorHandlingMiddleware(this.ctx, {
            debug: this.config.Debug.EnableDebug,
            uploadDump: this.config.Debug.UploadDump,
            pasteServiceUrl: "https://dump.yesimbot.chat/",
            includeFullSessionContent: false,
        }));
    }

    private addDatabaseStorage(manager: MiddlewareManager): void {
        manager.use(new DatabaseStorageMiddleware(this.ctx, {
            imageProcessor: this.services.getImageProcessor(),
            dataManager: this.services.getDataManager(),
        }));
    }

    private addReplyCondition(manager: MiddlewareManager): void {
        manager.use(new CheckReplyCondition(this.ctx, this.config.ReplyCondition));
    }

    private addLLMProcessing(manager: MiddlewareManager): void {
        manager.use(new LLMProcessingMiddleware(
            this.ctx,
            {
                chatModelSwitcher: this.services.getChatModelSwitcher(),
                promptBuilder: this.services.getPromptBuilder(),
            },
            {
                debug: this.config.Debug.EnableDebug,
                retryConfig: this.config.LLM.RetryConfig,
                adapterSwitchingConfig: this.config.LLM.AdapterSwitching,
            }
        ));
    }

    private addResponseHandling(manager: MiddlewareManager): void {
        manager.use(new ResponseHandlingMiddleware(
            this.ctx,
            { middlewareManager: manager },
            {
                maxRetry: this.config.ToolCall.MaxRetry,
                life: this.config.ToolCall.Life,
                maxHeartbeat: this.config.Chat.MaxHeartbeat,
            }
        ));
    }

    private registerCleanupHandlers(manager: MiddlewareManager): void {
        this.ctx.on("dispose", () => {
            const checkReply = manager.getMiddleware<CheckReplyCondition>("check-reply-condition");
            if (checkReply) {
                checkReply.destroy();
            }
        });
    }
}
